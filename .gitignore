# General
__pycache__/
*.pyc
.DS_Store
*.env
.venv
venv*/
venv
.vscode
*.ipynb
env/
venv/
!notebooks/jupyter/.gitkeep
.python-version
.mypy_cache
.ruff_cache
.pytest_cache
iframe_figures/
exports/*
.idea
.coverage
.scannerwork
htmlcov
**/.ipynb_checkpoints
*.swp
*.http
.coverage.*
*_tests.csv
*_sdk_audit.csv
!build/docker/compose.env
.dccache
*rome.json
**/node_modules/*
.cursorignore
darts_logs/
custom_imports/*.csv
custom_imports/*/*.csv
cache/
lightning_logs/
*/mocked_path
*.pem

# CLI
*.pyo
**/dist/*
build/cli
build/nsis/app
DMG/*
*.dmg
*.sh
cli/openbb_cli/assets/styles/user/hub.richstyle.json

# Platform
openbb_platform/openbb/package/*

# Dev Container env
obb/*

# OpenBB Distribution
!build/conda/installer/*.sh
*.pkg
*.exe
build/conda/tmp

# Bitcoin Quant Trading System specific
logs/*.log
data/processed/*
data/backtest_results/*
data/openbb_cache/*
results/*
models/*.pkl
models/*.joblib
models/*.h5
.streamlit/
*.db
!setup.sh
