#!/usr/bin/env python3
"""Test script for OpenBB Technical Indicators Integration.

This script tests the technical indicators functionality with the collected Bitcoin data.
"""

import logging
import sys
import os
from pathlib import Path
import warnings
warnings.filterwarnings('ignore')

# Add src to path
sys.path.append(str(Path(__file__).parent.parent / "src"))

import pandas as pd
import numpy as np
from datetime import datetime

from features.openbb_technical import OpenBBTechnicalIndicators
from data.openbb_client import OpenBBDataClient

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


def load_latest_bitcoin_data() -> pd.DataFrame:
    """Load the latest collected Bitcoin data."""
    data_dir = Path("data")
    
    # Find the latest Bitcoin data file
    bitcoin_files = list(data_dir.glob("BTCUSD_data_*.csv"))
    if not bitcoin_files:
        logger.error("No Bitcoin data files found")
        return pd.DataFrame()
    
    latest_file = max(bitcoin_files, key=lambda x: x.stat().st_mtime)
    logger.info(f"Loading data from {latest_file}")
    
    # Load and prepare data
    df = pd.read_csv(latest_file, index_col=0, parse_dates=True)
    
    # Ensure we have the required columns
    required_cols = ['open', 'high', 'low', 'close', 'volume']
    missing_cols = [col for col in required_cols if col not in df.columns]
    
    if missing_cols:
        logger.error(f"Missing required columns: {missing_cols}")
        return pd.DataFrame()
    
    logger.info(f"Loaded {len(df)} records from {df.index[0]} to {df.index[-1]}")
    return df


def test_technical_indicators():
    """Test technical indicators calculation."""
    logger.info("Starting technical indicators test")
    
    # Load Bitcoin data
    btc_data = load_latest_bitcoin_data()
    if btc_data.empty:
        logger.error("No data available for testing")
        return
    
    # Initialize technical indicators
    tech_indicators = OpenBBTechnicalIndicators()
    
    # Calculate all indicators
    logger.info("Calculating technical indicators...")
    data_with_indicators = tech_indicators.calculate_all_indicators(btc_data)
    
    # Display results
    logger.info(f"Added {len(data_with_indicators.columns) - len(btc_data.columns)} technical indicators")
    
    # Show latest values
    latest = data_with_indicators.iloc[-1]
    logger.info("Latest technical indicator values:")
    logger.info(f"  Price: ${latest['close']:.2f}")
    
    # RSI
    if 'rsi' in data_with_indicators.columns:
        rsi_value = latest['rsi']
        logger.info(f"  RSI: {rsi_value:.2f}")
        if rsi_value < 30:
            logger.info("    → Oversold condition")
        elif rsi_value > 70:
            logger.info("    → Overbought condition")
        else:
            logger.info("    → Neutral condition")
    
    # MACD
    if 'macd' in data_with_indicators.columns:
        macd = latest['macd']
        macd_signal = latest.get('macd_signal', 0)
        logger.info(f"  MACD: {macd:.2f}, Signal: {macd_signal:.2f}")
        if macd > macd_signal:
            logger.info("    → Bullish momentum")
        else:
            logger.info("    → Bearish momentum")
    
    # Bollinger Bands
    if all(col in data_with_indicators.columns for col in ['bb_upper', 'bb_lower']):
        bb_upper = latest['bb_upper']
        bb_lower = latest['bb_lower']
        price = latest['close']
        logger.info(f"  Bollinger Bands: Upper ${bb_upper:.2f}, Lower ${bb_lower:.2f}")
        if price > bb_upper:
            logger.info("    → Price above upper band (potential sell)")
        elif price < bb_lower:
            logger.info("    → Price below lower band (potential buy)")
        else:
            logger.info("    → Price within bands")
    
    # Moving Averages
    sma_20 = latest.get('sma_20', 0)
    sma_50 = latest.get('sma_50', 0)
    if sma_20 > 0 and sma_50 > 0:
        logger.info(f"  SMA 20: ${sma_20:.2f}, SMA 50: ${sma_50:.2f}")
        if sma_20 > sma_50:
            logger.info("    → Short-term uptrend")
        else:
            logger.info("    → Short-term downtrend")
    
    # Generate trading signals
    logger.info("Generating trading signals...")
    signals_data = tech_indicators.get_trading_signals(data_with_indicators)
    
    # Check recent signals
    recent_signals = signals_data.tail(5)
    signal_columns = [col for col in signals_data.columns if col.endswith(('_bullish', '_bearish', '_oversold', '_overbought', '_cross'))]
    
    logger.info("Recent trading signals:")
    for idx, row in recent_signals.iterrows():
        active_signals = []
        for col in signal_columns:
            if row.get(col, False):
                active_signals.append(col)
        
        if active_signals:
            logger.info(f"  {idx.date()}: {', '.join(active_signals)}")
    
    # Get indicator summary
    summary = tech_indicators.get_indicator_summary(data_with_indicators)
    logger.info("Technical Indicators Summary:")
    logger.info(f"  Timestamp: {summary.get('timestamp', 'N/A')}")
    logger.info(f"  Current Price: ${summary.get('price', 0):.2f}")
    logger.info(f"  Available Indicators: {len(summary.get('indicators', {}))}")
    
    # Save results
    output_file = f"data/btc_technical_indicators_{datetime.now().strftime('%Y%m%d_%H%M%S')}.csv"
    data_with_indicators.to_csv(output_file)
    logger.info(f"Saved technical indicators data to {output_file}")
    
    # Save signals
    signals_file = f"data/btc_trading_signals_{datetime.now().strftime('%Y%m%d_%H%M%S')}.csv"
    signals_data.to_csv(signals_file)
    logger.info(f"Saved trading signals to {signals_file}")
    
    return data_with_indicators, signals_data


def analyze_indicator_performance(data_with_indicators: pd.DataFrame):
    """Analyze the performance of technical indicators."""
    logger.info("Analyzing indicator performance...")
    
    if data_with_indicators.empty:
        return
    
    # Calculate some basic statistics
    stats = {}
    
    # RSI analysis
    if 'rsi' in data_with_indicators.columns:
        rsi_data = data_with_indicators['rsi'].dropna()
        stats['rsi'] = {
            'mean': rsi_data.mean(),
            'std': rsi_data.std(),
            'oversold_periods': (rsi_data < 30).sum(),
            'overbought_periods': (rsi_data > 70).sum(),
            'neutral_periods': ((rsi_data >= 30) & (rsi_data <= 70)).sum()
        }
    
    # MACD analysis
    if 'macd' in data_with_indicators.columns:
        macd_data = data_with_indicators['macd'].dropna()
        stats['macd'] = {
            'mean': macd_data.mean(),
            'std': macd_data.std(),
            'positive_periods': (macd_data > 0).sum(),
            'negative_periods': (macd_data < 0).sum()
        }
    
    # Bollinger Bands analysis
    if all(col in data_with_indicators.columns for col in ['close', 'bb_upper', 'bb_lower']):
        price = data_with_indicators['close']
        bb_upper = data_with_indicators['bb_upper']
        bb_lower = data_with_indicators['bb_lower']
        
        above_upper = (price > bb_upper).sum()
        below_lower = (price < bb_lower).sum()
        within_bands = len(price) - above_upper - below_lower
        
        stats['bollinger_bands'] = {
            'above_upper_band': above_upper,
            'below_lower_band': below_lower,
            'within_bands': within_bands,
            'band_width_mean': (bb_upper - bb_lower).mean()
        }
    
    # Display analysis
    logger.info("Technical Indicator Performance Analysis:")
    for indicator, metrics in stats.items():
        logger.info(f"  {indicator.upper()}:")
        for metric, value in metrics.items():
            if isinstance(value, float):
                logger.info(f"    {metric}: {value:.2f}")
            else:
                logger.info(f"    {metric}: {value}")


def main():
    """Main function to test technical indicators."""
    logger.info("Starting technical indicators test script")
    
    try:
        # Test technical indicators
        data_with_indicators, signals_data = test_technical_indicators()
        
        if not data_with_indicators.empty:
            # Analyze performance
            analyze_indicator_performance(data_with_indicators)
            
            logger.info("Technical indicators test completed successfully")
            logger.info(f"Total indicators calculated: {len(data_with_indicators.columns) - 5}")  # Subtract OHLCV columns
            logger.info(f"Data range: {data_with_indicators.index[0]} to {data_with_indicators.index[-1]}")
        else:
            logger.error("Technical indicators test failed")
            
    except Exception as e:
        logger.error(f"Test script failed: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
