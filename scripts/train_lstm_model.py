#!/usr/bin/env python3
"""
Train LSTM Model for Bitcoin Price Prediction

This script trains an LSTM neural network on Bitcoin data with technical indicators
and evaluates its performance for trading signal generation.
"""

import sys
import os
import pandas as pd
import numpy as np
from datetime import datetime
import json
from pathlib import Path
import matplotlib.pyplot as plt
import warnings
warnings.filterwarnings('ignore')

# Add src to path
sys.path.append(os.path.join(os.path.dirname(__file__), '..'))

from src.models.lstm import BitcoinLSTM, LSTMConfig

# GPU Configuration
import tensorflow as tf

def setup_gpu():
    """Setup GPU configuration for optimal training"""
    # Check GPU availability
    gpus = tf.config.experimental.list_physical_devices('GPU')
    if gpus:
        try:
            # Enable memory growth to avoid allocating all GPU memory at once
            for gpu in gpus:
                tf.config.experimental.set_memory_growth(gpu, True)
            print(f"✅ Found {len(gpus)} GPU(s): {[gpu.name for gpu in gpus]}")
            return True
        except RuntimeError as e:
            print(f"⚠️ GPU setup error: {e}")
            return False
    else:
        print("⚠️ No GPU found, using CPU")
        return False


def load_latest_bitcoin_data() -> pd.DataFrame:
    """Load the latest processed Bitcoin data"""
    data_dir = Path(__file__).parent.parent / "data" / "processed"
    
    # Find the latest processed file
    processed_files = list(data_dir.glob("BTCUSD_processed_*.csv"))
    if not processed_files:
        raise FileNotFoundError("No processed Bitcoin data found")
    
    latest_file = max(processed_files, key=lambda x: x.stat().st_mtime)
    print(f"Loading data from: {latest_file}")
    
    # Load and prepare data
    data = pd.read_csv(latest_file)
    data['Date'] = pd.to_datetime(data['Date'])
    data.set_index('Date', inplace=True)
    
    return data


def create_lstm_config(use_gpu: bool = False) -> LSTMConfig:
    """Create LSTM configuration optimized for available hardware"""
    # Adjust configuration based on hardware
    if use_gpu:
        # GPU optimized settings
        batch_size = 64
        epochs = 100
        lstm_units = [128, 64, 32]
        dense_units = [32, 16]
    else:
        # CPU optimized settings (lighter model)
        batch_size = 16
        epochs = 20
        lstm_units = [32, 16]
        dense_units = [8]

    return LSTMConfig(
        sequence_length=30,  # Reduced for faster training
        features=[
            'open', 'high', 'low', 'close', 'volume',
            'sma_20', 'sma_50', 'ema_12', 'ema_26',
            'rsi', 'macd', 'macd_signal',
            'bb_upper', 'bb_lower', 'atr'
        ],
        target='close',
        lstm_units=lstm_units,
        dropout_rate=0.2,
        dense_units=dense_units,
        batch_size=batch_size,
        epochs=epochs,
        validation_split=0.2,
        early_stopping_patience=10,
        learning_rate=0.001,
        prediction_horizon=1
    )


def plot_training_history(history: dict, save_path: str) -> None:
    """Plot training history"""
    fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(15, 5))
    
    # Loss plot
    ax1.plot(history['loss'], label='Training Loss')
    if 'val_loss' in history:
        ax1.plot(history['val_loss'], label='Validation Loss')
    ax1.set_title('Model Loss')
    ax1.set_xlabel('Epoch')
    ax1.set_ylabel('Loss')
    ax1.legend()
    ax1.grid(True)
    
    # MAE plot
    ax2.plot(history['mae'], label='Training MAE')
    if 'val_mae' in history:
        ax2.plot(history['val_mae'], label='Validation MAE')
    ax2.set_title('Model MAE')
    ax2.set_xlabel('Epoch')
    ax2.set_ylabel('MAE')
    ax2.legend()
    ax2.grid(True)
    
    plt.tight_layout()
    plt.savefig(save_path, dpi=300, bbox_inches='tight')
    plt.close()
    print(f"Training history plot saved to: {save_path}")


def plot_predictions(actual: list, predicted: list, save_path: str, n_points: int = 100) -> None:
    """Plot actual vs predicted prices"""
    # Use last n_points for better visualization
    actual = actual[-n_points:]
    predicted = predicted[-n_points:]
    
    plt.figure(figsize=(15, 8))
    
    x = range(len(actual))
    plt.plot(x, actual, label='Actual Price', color='blue', alpha=0.7)
    plt.plot(x, predicted, label='Predicted Price', color='red', alpha=0.7)
    
    plt.title(f'Bitcoin Price Prediction - Last {n_points} Days')
    plt.xlabel('Time Steps')
    plt.ylabel('Price ($)')
    plt.legend()
    plt.grid(True, alpha=0.3)
    
    # Add correlation coefficient
    correlation = np.corrcoef(actual, predicted)[0, 1]
    plt.text(0.02, 0.98, f'Correlation: {correlation:.3f}', 
             transform=plt.gca().transAxes, verticalalignment='top',
             bbox=dict(boxstyle='round', facecolor='white', alpha=0.8))
    
    plt.tight_layout()
    plt.savefig(save_path, dpi=300, bbox_inches='tight')
    plt.close()
    print(f"Predictions plot saved to: {save_path}")


def evaluate_trading_performance(signals_df: pd.DataFrame) -> dict:
    """Evaluate trading performance based on LSTM signals"""
    # Calculate returns based on signals
    signals_df = signals_df.copy()
    signals_df['next_price'] = signals_df['current_price'].shift(-1)
    signals_df['actual_return'] = (signals_df['next_price'] - signals_df['current_price']) / signals_df['current_price']
    
    # Calculate strategy returns
    signals_df['strategy_return'] = signals_df['signal'] * signals_df['actual_return']
    
    # Remove NaN values
    valid_signals = signals_df.dropna()
    
    if len(valid_signals) == 0:
        return {'error': 'No valid signals for evaluation'}
    
    # Calculate performance metrics
    total_return = (1 + valid_signals['strategy_return']).prod() - 1
    sharpe_ratio = valid_signals['strategy_return'].mean() / valid_signals['strategy_return'].std() * np.sqrt(252)
    
    # Win rate
    winning_trades = valid_signals[valid_signals['strategy_return'] > 0]
    win_rate = len(winning_trades) / len(valid_signals) if len(valid_signals) > 0 else 0
    
    # Max drawdown
    cumulative_returns = (1 + valid_signals['strategy_return']).cumprod()
    running_max = cumulative_returns.expanding().max()
    drawdown = (cumulative_returns - running_max) / running_max
    max_drawdown = drawdown.min()
    
    return {
        'total_return': total_return,
        'sharpe_ratio': sharpe_ratio,
        'win_rate': win_rate,
        'max_drawdown': max_drawdown,
        'num_trades': len(valid_signals),
        'num_buy_signals': len(valid_signals[valid_signals['signal'] == 1]),
        'num_sell_signals': len(valid_signals[valid_signals['signal'] == -1]),
        'avg_confidence': valid_signals['confidence'].mean()
    }


def main():
    """Main function to train LSTM model"""
    print("🚀 Bitcoin LSTM Model Training")
    print("=" * 50)

    try:
        # Setup GPU
        use_gpu = setup_gpu()

        # Load data
        data = load_latest_bitcoin_data()
        print(f"Loaded {len(data)} days of Bitcoin data")
        print(f"Date range: {data.index.min()} to {data.index.max()}")
        print(f"Price range: ${data['close'].min():.2f} to ${data['close'].max():.2f}")

        # Create LSTM configuration
        config = create_lstm_config(use_gpu)
        print(f"\nLSTM Configuration:")
        print(f"  Hardware: {'GPU' if use_gpu else 'CPU'}")
        print(f"  Sequence Length: {config.sequence_length}")
        print(f"  Features: {len(config.features)}")
        print(f"  LSTM Units: {config.lstm_units}")
        print(f"  Epochs: {config.epochs}")
        print(f"  Batch Size: {config.batch_size}")
        
        # Initialize LSTM model
        lstm_model = BitcoinLSTM(config)
        
        # Prepare data
        print(f"\n📊 Preparing data...")
        X_train, X_test, y_train, y_test = lstm_model.prepare_data(data)
        
        # Train model
        print(f"\n🎯 Training LSTM model...")
        training_history = lstm_model.train(X_train, y_train)
        
        # Evaluate model
        print(f"\n📈 Evaluating model performance...")
        evaluation_metrics = lstm_model.evaluate(X_test, y_test)
        
        # Generate trading signals
        print(f"\n💹 Generating trading signals...")
        signals_df = lstm_model.generate_trading_signals(data, threshold=0.02)
        
        # Evaluate trading performance
        trading_performance = evaluate_trading_performance(signals_df)
        
        # Create results directory
        results_dir = Path(__file__).parent.parent / "results"
        results_dir.mkdir(exist_ok=True)
        
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        
        # Save model
        model_path = results_dir / f"lstm_model_{timestamp}"
        lstm_model.save_model(str(model_path))
        
        # Plot training history
        if training_history:
            plot_path = results_dir / f"lstm_training_history_{timestamp}.png"
            plot_training_history(training_history, str(plot_path))
        
        # Plot predictions
        if 'predictions' in evaluation_metrics and 'actual' in evaluation_metrics:
            pred_plot_path = results_dir / f"lstm_predictions_{timestamp}.png"
            plot_predictions(
                evaluation_metrics['actual'], 
                evaluation_metrics['predictions'], 
                str(pred_plot_path)
            )
        
        # Save comprehensive results
        results = {
            'timestamp': timestamp,
            'data_info': {
                'num_days': len(data),
                'date_range': f"{data.index.min()} to {data.index.max()}",
                'price_range': f"${data['close'].min():.2f} to ${data['close'].max():.2f}",
                'features_used': config.features
            },
            'model_config': config.__dict__,
            'evaluation_metrics': evaluation_metrics,
            'trading_performance': trading_performance,
            'training_summary': {
                'epochs_trained': len(training_history['loss']) if training_history else 0,
                'final_loss': training_history['loss'][-1] if training_history else None,
                'final_val_loss': training_history.get('val_loss', [None])[-1] if training_history else None
            }
        }
        
        results_file = results_dir / f"lstm_results_{timestamp}.json"
        with open(results_file, 'w') as f:
            json.dump(results, f, indent=2, default=str)
        
        # Print summary
        print(f"\n{'='*60}")
        print("LSTM MODEL TRAINING RESULTS")
        print(f"{'='*60}")
        
        print(f"\n📊 Model Performance:")
        print(f"  RMSE: ${evaluation_metrics['rmse']:.2f}")
        print(f"  MAE: ${evaluation_metrics['mae']:.2f}")
        print(f"  MAPE: {evaluation_metrics['mape']:.2f}%")
        print(f"  Directional Accuracy: {evaluation_metrics['directional_accuracy']:.2f}%")
        
        if 'error' not in trading_performance:
            print(f"\n💹 Trading Performance:")
            print(f"  Total Return: {trading_performance['total_return']:.2%}")
            print(f"  Sharpe Ratio: {trading_performance['sharpe_ratio']:.3f}")
            print(f"  Win Rate: {trading_performance['win_rate']:.2%}")
            print(f"  Max Drawdown: {trading_performance['max_drawdown']:.2%}")
            print(f"  Number of Trades: {trading_performance['num_trades']}")
            print(f"  Buy Signals: {trading_performance['num_buy_signals']}")
            print(f"  Sell Signals: {trading_performance['num_sell_signals']}")
        
        print(f"\n📁 Results saved to: {results_file}")
        print(f"🤖 Model saved to: {model_path}")
        
        print(f"\n✅ LSTM training completed successfully!")
        
    except Exception as e:
        print(f"❌ Error in LSTM training: {str(e)}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
