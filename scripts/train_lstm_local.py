#!/usr/bin/env python3
"""
Lightweight LSTM Training for Local CPU

This script provides a fast, CPU-optimized version of LSTM training
suitable for local development and testing.
"""

import sys
import os
import pandas as pd
import numpy as np
from datetime import datetime
import json
from pathlib import Path
import warnings
warnings.filterwarnings('ignore')

# Add src to path
sys.path.append(os.path.join(os.path.dirname(__file__), '..'))

from src.models.lstm import BitcoinLSTM, LSTMConfig


def load_latest_bitcoin_data() -> pd.DataFrame:
    """Load the latest processed Bitcoin data"""
    data_dir = Path(__file__).parent.parent / "data" / "processed"
    
    # Find the latest processed file
    processed_files = list(data_dir.glob("BTCUSD_processed_*.csv"))
    if not processed_files:
        raise FileNotFoundError("No processed Bitcoin data found")
    
    latest_file = max(processed_files, key=lambda x: x.stat().st_mtime)
    print(f"Loading data from: {latest_file}")
    
    # Load and prepare data
    data = pd.read_csv(latest_file)
    data['Date'] = pd.to_datetime(data['Date'])
    data.set_index('Date', inplace=True)
    
    # Use only recent data for faster training (last 6 months)
    data = data.tail(180)
    
    return data


def create_lightweight_config() -> LSTMConfig:
    """Create lightweight LSTM configuration for CPU training"""
    return LSTMConfig(
        sequence_length=20,  # Shorter sequences
        features=[
            'close', 'volume',  # Minimal features
            'sma_20', 'sma_50',
            'rsi', 'macd'
        ],
        target='close',
        lstm_units=[16],  # Single small LSTM layer
        dropout_rate=0.1,
        dense_units=[8],  # Small dense layer
        batch_size=8,  # Small batch size
        epochs=10,  # Few epochs for quick training
        validation_split=0.2,
        early_stopping_patience=5,
        learning_rate=0.01,  # Higher learning rate for faster convergence
        prediction_horizon=1
    )


def quick_evaluate_trading_performance(signals_df: pd.DataFrame) -> dict:
    """Quick evaluation of trading performance"""
    # Calculate simple returns
    signals_df = signals_df.copy()
    signals_df['price_change'] = signals_df['current_price'].pct_change()
    
    # Calculate strategy returns (simplified)
    buy_signals = signals_df[signals_df['signal'] == 1]
    sell_signals = signals_df[signals_df['signal'] == -1]
    
    return {
        'num_buy_signals': len(buy_signals),
        'num_sell_signals': len(sell_signals),
        'avg_buy_confidence': buy_signals['confidence'].mean() if len(buy_signals) > 0 else 0,
        'avg_sell_confidence': sell_signals['confidence'].mean() if len(sell_signals) > 0 else 0,
        'total_signals': len(signals_df[signals_df['signal'] != 0])
    }


def main():
    """Main function for lightweight LSTM training"""
    print("🚀 Bitcoin LSTM Local Training (CPU Optimized)")
    print("=" * 55)
    
    try:
        # Load data
        data = load_latest_bitcoin_data()
        print(f"Loaded {len(data)} days of Bitcoin data (recent subset)")
        print(f"Date range: {data.index.min()} to {data.index.max()}")
        print(f"Price range: ${data['close'].min():.2f} to ${data['close'].max():.2f}")
        
        # Create lightweight configuration
        config = create_lightweight_config()
        print(f"\nLightweight LSTM Configuration:")
        print(f"  Hardware: CPU Optimized")
        print(f"  Sequence Length: {config.sequence_length}")
        print(f"  Features: {len(config.features)} - {config.features}")
        print(f"  LSTM Units: {config.lstm_units}")
        print(f"  Epochs: {config.epochs}")
        print(f"  Batch Size: {config.batch_size}")
        
        # Initialize LSTM model
        lstm_model = BitcoinLSTM(config)
        
        # Prepare data
        print(f"\n📊 Preparing data...")
        X_train, X_test, y_train, y_test = lstm_model.prepare_data(data)
        
        # Train model
        print(f"\n🎯 Training lightweight LSTM model...")
        start_time = datetime.now()
        training_history = lstm_model.train(X_train, y_train)
        training_time = datetime.now() - start_time
        
        print(f"⏱️ Training completed in: {training_time}")
        
        # Quick evaluation
        print(f"\n📈 Evaluating model performance...")
        evaluation_metrics = lstm_model.evaluate(X_test, y_test)
        
        # Generate trading signals
        print(f"\n💹 Generating trading signals...")
        signals_df = lstm_model.generate_trading_signals(data, threshold=0.01)  # Lower threshold
        
        # Quick trading performance evaluation
        trading_performance = quick_evaluate_trading_performance(signals_df)
        
        # Create results directory
        results_dir = Path(__file__).parent.parent / "results"
        results_dir.mkdir(exist_ok=True)
        
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        
        # Save lightweight results
        results = {
            'timestamp': timestamp,
            'training_type': 'lightweight_cpu',
            'training_time_seconds': training_time.total_seconds(),
            'data_info': {
                'num_days': len(data),
                'date_range': f"{data.index.min()} to {data.index.max()}",
                'price_range': f"${data['close'].min():.2f} to ${data['close'].max():.2f}",
                'features_used': config.features
            },
            'model_config': config.__dict__,
            'evaluation_metrics': {
                'rmse': evaluation_metrics['rmse'],
                'mae': evaluation_metrics['mae'],
                'mape': evaluation_metrics['mape'],
                'directional_accuracy': evaluation_metrics['directional_accuracy']
            },
            'trading_performance': trading_performance,
            'training_summary': {
                'epochs_trained': len(training_history['loss']) if training_history else 0,
                'final_loss': training_history['loss'][-1] if training_history else None,
                'final_val_loss': training_history.get('val_loss', [None])[-1] if training_history else None
            }
        }
        
        results_file = results_dir / f"lstm_local_results_{timestamp}.json"
        with open(results_file, 'w') as f:
            json.dump(results, f, indent=2, default=str)
        
        # Save lightweight model
        model_path = results_dir / f"lstm_local_model_{timestamp}"
        lstm_model.save_model(str(model_path))
        
        # Print summary
        print(f"\n{'='*60}")
        print("LIGHTWEIGHT LSTM TRAINING RESULTS")
        print(f"{'='*60}")
        
        print(f"\n⏱️ Training Time: {training_time}")
        print(f"📊 Model Performance:")
        print(f"  RMSE: ${evaluation_metrics['rmse']:.2f}")
        print(f"  MAE: ${evaluation_metrics['mae']:.2f}")
        print(f"  MAPE: {evaluation_metrics['mape']:.2f}%")
        print(f"  Directional Accuracy: {evaluation_metrics['directional_accuracy']:.2f}%")
        
        print(f"\n💹 Trading Signals:")
        print(f"  Total Signals: {trading_performance['total_signals']}")
        print(f"  Buy Signals: {trading_performance['num_buy_signals']}")
        print(f"  Sell Signals: {trading_performance['num_sell_signals']}")
        print(f"  Avg Buy Confidence: {trading_performance['avg_buy_confidence']:.3f}")
        print(f"  Avg Sell Confidence: {trading_performance['avg_sell_confidence']:.3f}")
        
        print(f"\n📁 Results saved to: {results_file}")
        print(f"🤖 Model saved to: {model_path}")
        
        print(f"\n✅ Lightweight LSTM training completed successfully!")
        print(f"💡 For full GPU training, see docs/GPU_TRAINING_GUIDE.md")
        
        # Show recent signals
        recent_signals = signals_df[signals_df['signal'] != 0].tail(5)
        if len(recent_signals) > 0:
            print(f"\n🔍 Recent Trading Signals:")
            for idx, row in recent_signals.iterrows():
                signal_emoji = "🟢" if row['signal'] == 1 else "🔴"
                print(f"  {signal_emoji} {idx.strftime('%Y-%m-%d')}: {row['signal_name']} "
                      f"${row['current_price']:.2f} → ${row['predicted_price']:.2f} "
                      f"({row['price_change_pct']:+.2f}%) Conf: {row['confidence']:.3f}")
        
    except Exception as e:
        print(f"❌ Error in lightweight LSTM training: {str(e)}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
