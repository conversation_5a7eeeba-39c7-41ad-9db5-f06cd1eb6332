#!/usr/bin/env python3
"""
Test Baseline Trading Strategies

This script tests all baseline trading strategies on Bitcoin data and compares their performance.
"""

import sys
import os
import pandas as pd
import numpy as np
from datetime import datetime
import json
from pathlib import Path

# Add src to path
sys.path.append(os.path.join(os.path.dirname(__file__), '..'))

from src.models.baselines import (
    MovingAverageCrossover,
    RSIStrategy,
    MACDStrategy,
    BollingerBandsStrategy,
    MultiIndicatorStrategy
)


def load_latest_bitcoin_data() -> pd.DataFrame:
    """Load the latest processed Bitcoin data"""
    data_dir = Path(__file__).parent.parent / "data" / "processed"
    
    # Find the latest processed file
    processed_files = list(data_dir.glob("BTCUSD_processed_*.csv"))
    if not processed_files:
        raise FileNotFoundError("No processed Bitcoin data found")
    
    latest_file = max(processed_files, key=lambda x: x.stat().st_mtime)
    print(f"Loading data from: {latest_file}")
    
    # Load and prepare data
    data = pd.read_csv(latest_file)
    data['Date'] = pd.to_datetime(data['Date'])
    data.set_index('Date', inplace=True)
    
    return data


def test_strategy(strategy, data: pd.DataFrame, strategy_name: str) -> dict:
    """Test a single strategy and return results"""
    print(f"\n{'='*50}")
    print(f"Testing {strategy_name}")
    print(f"{'='*50}")
    
    try:
        # Generate signals
        signals = strategy.generate_signals(data)
        print(f"Generated {len(signals)} signals")
        
        # Run backtest
        performance = strategy.backtest(data)
        
        # Display results
        print(f"\nPerformance Metrics:")
        print(f"  Total Return: {performance['total_return']:.2%}")
        print(f"  Sharpe Ratio: {performance['sharpe_ratio']:.3f}")
        print(f"  Max Drawdown: {performance['max_drawdown']:.2%}")
        print(f"  Win Rate: {performance['win_rate']:.2%}")
        print(f"  Number of Trades: {performance['num_trades']}")
        print(f"  Average Confidence: {performance['avg_confidence']:.3f}")
        
        # Show sample signals
        if signals:
            print(f"\nSample Signals (first 5):")
            for i, signal in enumerate(signals[:5]):
                print(f"  {i+1}. {signal.timestamp.strftime('%Y-%m-%d')} | "
                      f"{signal.signal.name} | ${signal.price:.2f} | "
                      f"Conf: {signal.confidence:.3f} | {signal.reason}")
        
        return {
            'strategy_name': strategy_name,
            'performance': performance,
            'num_signals': len(signals),
            'sample_signals': [
                {
                    'timestamp': signal.timestamp.isoformat(),
                    'signal': signal.signal.name,
                    'price': signal.price,
                    'confidence': signal.confidence,
                    'reason': signal.reason
                } for signal in signals[:10]  # Store first 10 signals
            ]
        }
        
    except Exception as e:
        print(f"Error testing {strategy_name}: {str(e)}")
        return {
            'strategy_name': strategy_name,
            'error': str(e),
            'performance': {},
            'num_signals': 0,
            'sample_signals': []
        }


def compare_strategies(results: list) -> None:
    """Compare strategy performance"""
    print(f"\n{'='*80}")
    print("STRATEGY COMPARISON")
    print(f"{'='*80}")
    
    # Create comparison table
    comparison_data = []
    for result in results:
        if 'error' not in result and result['performance']:
            perf = result['performance']
            comparison_data.append({
                'Strategy': result['strategy_name'],
                'Total Return': f"{perf['total_return']:.2%}",
                'Sharpe Ratio': f"{perf['sharpe_ratio']:.3f}",
                'Max Drawdown': f"{perf['max_drawdown']:.2%}",
                'Win Rate': f"{perf['win_rate']:.2%}",
                'Trades': perf['num_trades'],
                'Avg Confidence': f"{perf['avg_confidence']:.3f}"
            })
    
    if comparison_data:
        df_comparison = pd.DataFrame(comparison_data)
        print(df_comparison.to_string(index=False))
        
        # Find best performing strategy
        best_sharpe = max(results, key=lambda x: x.get('performance', {}).get('sharpe_ratio', -999))
        best_return = max(results, key=lambda x: x.get('performance', {}).get('total_return', -999))
        
        print(f"\n🏆 Best Sharpe Ratio: {best_sharpe['strategy_name']} "
              f"({best_sharpe['performance']['sharpe_ratio']:.3f})")
        print(f"🏆 Best Total Return: {best_return['strategy_name']} "
              f"({best_return['performance']['total_return']:.2%})")


def save_results(results: list, data_info: dict) -> None:
    """Save test results to file"""
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    results_file = Path(__file__).parent.parent / "results" / f"baseline_strategies_test_{timestamp}.json"
    
    # Create results directory if it doesn't exist
    results_file.parent.mkdir(exist_ok=True)
    
    output = {
        'test_timestamp': timestamp,
        'data_info': data_info,
        'strategy_results': results
    }
    
    with open(results_file, 'w') as f:
        json.dump(output, f, indent=2, default=str)
    
    print(f"\n📊 Results saved to: {results_file}")


def main():
    """Main function to test all baseline strategies"""
    print("🚀 Bitcoin Baseline Strategies Testing")
    print("=" * 50)
    
    try:
        # Load data
        data = load_latest_bitcoin_data()
        print(f"Loaded {len(data)} days of Bitcoin data")
        print(f"Date range: {data.index.min()} to {data.index.max()}")
        print(f"Price range: ${data['close'].min():.2f} to ${data['close'].max():.2f}")
        
        data_info = {
            'num_days': len(data),
            'date_range': f"{data.index.min()} to {data.index.max()}",
            'price_range': f"${data['close'].min():.2f} to ${data['close'].max():.2f}",
            'current_price': f"${data['close'].iloc[-1]:.2f}"
        }
        
        # Initialize strategies
        strategies = [
            (MovingAverageCrossover(20, 50), "Moving Average Crossover (20/50)"),
            (MovingAverageCrossover(50, 200), "Moving Average Crossover (50/200)"),
            (RSIStrategy(30, 70), "RSI Strategy (30/70)"),
            (RSIStrategy(20, 80), "RSI Strategy (20/80)"),
            (MACDStrategy(), "MACD Crossover"),
            (BollingerBandsStrategy(0.01), "Bollinger Bands (1% threshold)"),
            (BollingerBandsStrategy(0.005), "Bollinger Bands (0.5% threshold)"),
            (MultiIndicatorStrategy(), "Multi-Indicator Consensus")
        ]
        
        # Test all strategies
        results = []
        for strategy, name in strategies:
            result = test_strategy(strategy, data, name)
            results.append(result)
        
        # Compare strategies
        compare_strategies(results)
        
        # Save results
        save_results(results, data_info)
        
        print(f"\n✅ Testing completed successfully!")
        print(f"📈 Tested {len(strategies)} strategies on {len(data)} days of Bitcoin data")
        
    except Exception as e:
        print(f"❌ Error in main execution: {str(e)}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
