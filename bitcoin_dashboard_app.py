"""Bitcoin Trading Dashboard with Real-time Data Visualization.

Professional-grade Streamlit dashboard for Bitcoin quantitative trading analysis
with real-time data ingestion, technical indicators, and trading signals.
"""

import streamlit as st
import pandas as pd
import numpy as np
import plotly.graph_objects as go
import plotly.express as px
from plotly.subplots import make_subplots
import json
import sys
from pathlib import Path
from datetime import datetime, timedelta
import warnings
warnings.filterwarnings('ignore')

# Add src to path for imports
sys.path.append(str(Path(__file__).parent / "src"))

try:
    from data.openbb_client import OpenBBDataClient
    from data.crypto_providers import CryptoProviderManager
    from data.data_validation import DataValidator
    from features.openbb_technical import OpenBBTechnicalIndicators
    PIPELINE_AVAILABLE = True
except ImportError:
    PIPELINE_AVAILABLE = False
    import yfinance as yf

# Page configuration
st.set_page_config(
    page_title="Bitcoin Quant Trading Dashboard",
    page_icon="₿",
    layout="wide",
    initial_sidebar_state="expanded"
)

# Custom CSS for professional styling
st.markdown("""
<style>
    .main-header {
        font-size: 2.5rem;
        font-weight: bold;
        color: #f7931a;
        text-align: center;
        margin-bottom: 2rem;
    }
    .metric-card {
        background-color: #f0f2f6;
        padding: 1rem;
        border-radius: 0.5rem;
        border-left: 4px solid #f7931a;
    }
    .status-good {
        color: #28a745;
        font-weight: bold;
    }
    .status-warning {
        color: #ffc107;
        font-weight: bold;
    }
    .status-danger {
        color: #dc3545;
        font-weight: bold;
    }
    .sidebar-info {
        background-color: #e8f4f8;
        padding: 1rem;
        border-radius: 0.5rem;
        margin: 1rem 0;
    }
</style>
""", unsafe_allow_html=True)


@st.cache_data(ttl=300)  # Cache for 5 minutes
def load_latest_bitcoin_data():
    """Load the latest Bitcoin data with caching."""
    try:
        # Find the latest processed data file
        data_dir = Path("data/processed")
        if not data_dir.exists():
            data_dir = Path("data")
        
        bitcoin_files = list(data_dir.glob("*BTC*processed*.csv")) or list(data_dir.glob("*BTC*data*.csv"))
        
        if not bitcoin_files:
            # Fallback to yfinance
            ticker = yf.Ticker("BTC-USD")
            data = ticker.history(period="1y", interval="1d")
            # Standardize column names
            data.columns = [col.lower() for col in data.columns]
            return data
        
        latest_file = max(bitcoin_files, key=lambda x: x.stat().st_mtime)
        
        # Load data
        df = pd.read_csv(latest_file, index_col=0, parse_dates=True)
        return df
        
    except Exception as e:
        st.error(f"Error loading Bitcoin data: {e}")
        # Fallback to yfinance
        try:
            ticker = yf.Ticker("BTC-USD")
            data = ticker.history(period="1y", interval="1d")
            data.columns = [col.lower() for col in data.columns]
            return data
        except:
            return pd.DataFrame()


@st.cache_data(ttl=300)
def load_latest_report():
    """Load the latest pipeline report."""
    try:
        reports_dir = Path("reports")
        if not reports_dir.exists():
            return {}
        
        report_files = list(reports_dir.glob("bitcoin_pipeline_report_*.json"))
        if not report_files:
            return {}
        
        latest_report = max(report_files, key=lambda x: x.stat().st_mtime)
        
        with open(latest_report, 'r') as f:
            return json.load(f)
            
    except Exception as e:
        st.error(f"Error loading report: {e}")
        return {}


def create_price_chart(data):
    """Create interactive price chart with technical indicators."""
    if data.empty:
        return go.Figure()
    
    # Create subplots
    fig = make_subplots(
        rows=4, cols=1,
        shared_xaxes=True,
        vertical_spacing=0.05,
        subplot_titles=('Bitcoin Price & Technical Indicators', 'Volume', 'RSI', 'MACD'),
        row_heights=[0.5, 0.2, 0.15, 0.15]
    )
    
    # Main price chart with candlesticks
    fig.add_trace(
        go.Candlestick(
            x=data.index,
            open=data['open'],
            high=data['high'],
            low=data['low'],
            close=data['close'],
            name='Bitcoin Price',
            increasing_line_color='#26a69a',
            decreasing_line_color='#ef5350'
        ),
        row=1, col=1
    )
    
    # Add moving averages if available
    if 'sma_20' in data.columns:
        fig.add_trace(
            go.Scatter(x=data.index, y=data['sma_20'], name='SMA 20', 
                      line=dict(color='orange', width=1)),
            row=1, col=1
        )
    
    if 'sma_50' in data.columns:
        fig.add_trace(
            go.Scatter(x=data.index, y=data['sma_50'], name='SMA 50', 
                      line=dict(color='blue', width=1)),
            row=1, col=1
        )
    
    # Add Bollinger Bands if available
    if all(col in data.columns for col in ['bb_upper', 'bb_lower', 'bb_middle']):
        fig.add_trace(
            go.Scatter(x=data.index, y=data['bb_upper'], name='BB Upper',
                      line=dict(color='gray', width=1, dash='dash')),
            row=1, col=1
        )
        fig.add_trace(
            go.Scatter(x=data.index, y=data['bb_lower'], name='BB Lower',
                      line=dict(color='gray', width=1, dash='dash'),
                      fill='tonexty', fillcolor='rgba(128,128,128,0.1)'),
            row=1, col=1
        )
    
    # Volume chart
    colors = ['red' if close < open else 'green' 
              for close, open in zip(data['close'], data['open'])]
    
    fig.add_trace(
        go.Bar(x=data.index, y=data['volume'], name='Volume',
               marker_color=colors, opacity=0.7),
        row=2, col=1
    )
    
    # RSI chart
    if 'rsi' in data.columns:
        fig.add_trace(
            go.Scatter(x=data.index, y=data['rsi'], name='RSI',
                      line=dict(color='purple', width=2)),
            row=3, col=1
        )
        # Add RSI levels
        fig.add_hline(y=70, line_dash="dash", line_color="red", row=3, col=1)
        fig.add_hline(y=30, line_dash="dash", line_color="green", row=3, col=1)
        fig.add_hline(y=50, line_dash="dot", line_color="gray", row=3, col=1)
    
    # MACD chart
    if all(col in data.columns for col in ['macd', 'macd_signal', 'macd_histogram']):
        fig.add_trace(
            go.Scatter(x=data.index, y=data['macd'], name='MACD',
                      line=dict(color='blue', width=2)),
            row=4, col=1
        )
        fig.add_trace(
            go.Scatter(x=data.index, y=data['macd_signal'], name='MACD Signal',
                      line=dict(color='red', width=2)),
            row=4, col=1
        )
        fig.add_trace(
            go.Bar(x=data.index, y=data['macd_histogram'], name='MACD Histogram',
                   marker_color='gray', opacity=0.6),
            row=4, col=1
        )
    
    # Update layout
    fig.update_layout(
        title="Bitcoin Trading Analysis",
        height=800,
        showlegend=True,
        xaxis_rangeslider_visible=False,
        template="plotly_white"
    )
    
    # Update y-axes
    fig.update_yaxes(title_text="Price ($)", row=1, col=1)
    fig.update_yaxes(title_text="Volume", row=2, col=1)
    fig.update_yaxes(title_text="RSI", row=3, col=1, range=[0, 100])
    fig.update_yaxes(title_text="MACD", row=4, col=1)
    
    return fig


def display_key_metrics(data, report):
    """Display key trading metrics."""
    if data.empty:
        return
    
    latest = data.iloc[-1]
    previous = data.iloc[-2] if len(data) > 1 else latest
    
    col1, col2, col3, col4 = st.columns(4)
    
    with col1:
        price_change = latest['close'] - previous['close']
        price_change_pct = (price_change / previous['close']) * 100
        
        st.metric(
            label="Bitcoin Price",
            value=f"${latest['close']:,.2f}",
            delta=f"{price_change_pct:+.2f}%"
        )
    
    with col2:
        if 'rsi' in data.columns and not pd.isna(latest['rsi']):
            rsi_value = latest['rsi']
            rsi_status = "Overbought" if rsi_value > 70 else "Oversold" if rsi_value < 30 else "Neutral"
            st.metric(
                label="RSI (14)",
                value=f"{rsi_value:.1f}",
                delta=rsi_status
            )
        else:
            st.metric(label="RSI (14)", value="N/A")
    
    with col3:
        if 'volume' in data.columns:
            volume_24h = latest['volume']
            st.metric(
                label="24h Volume",
                value=f"{volume_24h:,.0f}"
            )
        else:
            st.metric(label="24h Volume", value="N/A")
    
    with col4:
        if report and 'data_validation' in report:
            quality_score = report['data_validation'].get('quality_score', 0)
            st.metric(
                label="Data Quality",
                value=f"{quality_score:.1f}/100",
                delta="PASS" if quality_score >= 80 else "FAIL"
            )
        else:
            st.metric(label="Data Quality", value="N/A")


def display_trading_signals(data):
    """Display current trading signals."""
    if data.empty or len(data) < 2:
        st.warning("Insufficient data for trading signals")
        return

    st.subheader("🎯 Trading Signals")

    latest = data.iloc[-1]
    signals = []

    # RSI signals
    if 'rsi' in data.columns and not pd.isna(latest['rsi']):
        rsi = latest['rsi']
        if rsi > 70:
            signals.append(("RSI Overbought", "🔴 SELL Signal", "danger"))
        elif rsi < 30:
            signals.append(("RSI Oversold", "🟢 BUY Signal", "good"))
        else:
            signals.append(("RSI Neutral", "⚪ HOLD", "warning"))

    # MACD signals
    if all(col in data.columns for col in ['macd', 'macd_signal']):
        macd = latest['macd']
        macd_signal = latest['macd_signal']
        if macd > macd_signal:
            signals.append(("MACD Bullish", "🟢 BUY Signal", "good"))
        else:
            signals.append(("MACD Bearish", "🔴 SELL Signal", "danger"))

    # Bollinger Bands signals
    if all(col in data.columns for col in ['close', 'bb_upper', 'bb_lower']):
        price = latest['close']
        bb_upper = latest['bb_upper']
        bb_lower = latest['bb_lower']

        if price > bb_upper:
            signals.append(("BB Breakout", "🔴 Potential SELL", "danger"))
        elif price < bb_lower:
            signals.append(("BB Squeeze", "🟢 Potential BUY", "good"))
        else:
            signals.append(("BB Normal", "⚪ Within Bands", "warning"))

    # Display signals
    if signals:
        cols = st.columns(len(signals))
        for i, (indicator, signal, status) in enumerate(signals):
            with cols[i]:
                status_class = f"status-{status}"
                st.markdown(f"""
                <div class="metric-card">
                    <h4>{indicator}</h4>
                    <p class="{status_class}">{signal}</p>
                </div>
                """, unsafe_allow_html=True)
    else:
        st.info("No trading signals available. Technical indicators may be calculating.")


def main():
    """Main dashboard function."""
    # Header
    st.markdown('<h1 class="main-header">₿ Bitcoin Quantitative Trading Dashboard</h1>',
                unsafe_allow_html=True)

    # Sidebar
    with st.sidebar:
        st.markdown('<div class="sidebar-info">', unsafe_allow_html=True)
        st.markdown("### 📊 Dashboard Controls")

        # Auto-refresh toggle
        auto_refresh = st.checkbox("Auto-refresh (5 min)", value=True)

        # Manual refresh button
        if st.button("🔄 Refresh Data"):
            st.cache_data.clear()
            st.rerun()

        # Data range selector
        st.markdown("### 📅 Data Range")
        days_to_show = st.selectbox(
            "Show last:",
            [30, 60, 90, 180, 365],
            index=2,
            format_func=lambda x: f"{x} days"
        )

        st.markdown('</div>', unsafe_allow_html=True)

        # System status
        st.markdown("### 🔧 System Status")

        # Check if data pipeline is working
        data_dir = Path("data")
        reports_dir = Path("reports")

        if data_dir.exists() and any(data_dir.glob("*BTC*")):
            st.success("✅ Data Pipeline Active")
        else:
            st.error("❌ No Bitcoin Data Found")

        if reports_dir.exists() and any(reports_dir.glob("*.json")):
            st.success("✅ Reports Available")
        else:
            st.warning("⚠️ No Reports Found")

    # Load data
    with st.spinner("Loading Bitcoin data..."):
        data = load_latest_bitcoin_data()
        report = load_latest_report()

    if data.empty:
        st.error("No Bitcoin data available. Please run the data collection pipeline first.")
        st.code("python scripts/bitcoin_data_pipeline.py")
        return

    # Filter data by selected range
    if len(data) > days_to_show:
        data = data.tail(days_to_show)

    # Display key metrics
    display_key_metrics(data, report)

    # Main chart
    st.subheader("📈 Price Analysis & Technical Indicators")
    chart = create_price_chart(data)
    st.plotly_chart(chart, use_container_width=True)

    # Trading signals
    display_trading_signals(data)

    # Data quality section
    col1, col2 = st.columns(2)

    with col1:
        st.subheader("📋 Data Summary")
        if not data.empty:
            st.write(f"**Data Range:** {data.index[0].date()} to {data.index[-1].date()}")
            st.write(f"**Total Records:** {len(data):,}")
            st.write(f"**Latest Update:** {data.index[-1]}")

            # Technical indicators summary
            indicator_cols = [col for col in data.columns
                            if col not in ['open', 'high', 'low', 'close', 'volume', 'Dividends', 'Stock Splits']]
            st.write(f"**Technical Indicators:** {len(indicator_cols)}")

    with col2:
        st.subheader("🎯 Pipeline Report")
        if report:
            if 'data_collection' in report:
                dc = report['data_collection']
                st.write(f"**Data Provider:** {dc.get('best_provider', 'N/A')}")
                st.write(f"**Records Collected:** {dc.get('record_count', 0):,}")

            if 'data_validation' in report:
                dv = report['data_validation']
                quality_score = dv.get('quality_score', 0)
                status = "✅ PASS" if dv.get('is_acceptable', False) else "❌ FAIL"
                st.write(f"**Data Quality:** {quality_score:.1f}/100 {status}")

            if 'technical_analysis' in report:
                ta = report['technical_analysis']
                st.write(f"**Indicators Added:** {ta.get('indicators_added', 0)}")
        else:
            st.info("No pipeline report available")

    # Footer
    st.markdown("---")
    st.markdown(
        "**Bitcoin Quantitative Trading Dashboard** | "
        f"Last updated: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')} | "
        "Data provided by multi-provider pipeline with OpenBB integration"
    )

    # Auto-refresh
    if auto_refresh:
        st.rerun()


if __name__ == "__main__":
    main()
