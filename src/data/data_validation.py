"""Data validation and quality assurance for cryptocurrency data."""

import logging
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple

import numpy as np
import pandas as pd
from pydantic import BaseModel

logger = logging.getLogger(__name__)


class DataQualityMetrics(BaseModel):
    """Data quality metrics model."""
    
    completeness: float
    accuracy: float
    consistency: float
    timeliness: float
    validity: float
    overall_score: float


class DataValidator:
    """Validates cryptocurrency data quality."""
    
    def __init__(self, config: Optional[Dict] = None):
        """Initialize data validator.
        
        Args:
            config: Validation configuration
        """
        self.config = config or self._get_default_config()
    
    def _get_default_config(self) -> Dict:
        """Get default validation configuration."""
        return {
            "max_price_change_percent": 20.0,
            "min_volume_threshold": 0,
            "max_gap_hours": 25,  # For daily data
            "outlier_std_threshold": 3.0,
            "min_data_points": 10,
            "required_columns": ["open", "high", "low", "close", "volume"]
        }
    
    def validate_dataframe(self, df: pd.DataFrame) -> DataQualityMetrics:
        """Validate a DataFrame and return quality metrics.
        
        Args:
            df: DataFrame to validate
            
        Returns:
            DataQualityMetrics object
        """
        metrics = {
            "completeness": self._check_completeness(df),
            "accuracy": self._check_accuracy(df),
            "consistency": self._check_consistency(df),
            "timeliness": self._check_timeliness(df),
            "validity": self._check_validity(df)
        }
        
        # Calculate overall score (weighted average)
        weights = {
            "completeness": 0.25,
            "accuracy": 0.25,
            "consistency": 0.20,
            "timeliness": 0.15,
            "validity": 0.15
        }
        
        overall_score = sum(metrics[key] * weights[key] for key in weights)
        metrics["overall_score"] = overall_score
        
        return DataQualityMetrics(**metrics)
    
    def _check_completeness(self, df: pd.DataFrame) -> float:
        """Check data completeness (missing values).
        
        Args:
            df: DataFrame to check
            
        Returns:
            Completeness score (0-100)
        """
        if df.empty:
            return 0.0
        
        total_cells = df.size
        missing_cells = df.isnull().sum().sum()
        completeness = ((total_cells - missing_cells) / total_cells) * 100
        
        return min(100.0, max(0.0, completeness))
    
    def _check_accuracy(self, df: pd.DataFrame) -> float:
        """Check data accuracy (OHLC relationships, price reasonableness).
        
        Args:
            df: DataFrame to check
            
        Returns:
            Accuracy score (0-100)
        """
        if df.empty or not all(col in df.columns for col in ["open", "high", "low", "close"]):
            return 0.0
        
        accuracy_issues = 0
        total_checks = 0
        
        # Check OHLC relationships
        for idx, row in df.iterrows():
            total_checks += 3
            
            # High should be >= Open, Close, Low
            if not (row["high"] >= row["open"] and 
                   row["high"] >= row["close"] and 
                   row["high"] >= row["low"]):
                accuracy_issues += 1
            
            # Low should be <= Open, Close, High
            if not (row["low"] <= row["open"] and 
                   row["low"] <= row["close"] and 
                   row["low"] <= row["high"]):
                accuracy_issues += 1
            
            # Check for negative prices
            if any(row[col] <= 0 for col in ["open", "high", "low", "close"]):
                accuracy_issues += 1
        
        if total_checks == 0:
            return 0.0
        
        accuracy = ((total_checks - accuracy_issues) / total_checks) * 100
        return min(100.0, max(0.0, accuracy))
    
    def _check_consistency(self, df: pd.DataFrame) -> float:
        """Check data consistency (outliers, extreme changes).
        
        Args:
            df: DataFrame to check
            
        Returns:
            Consistency score (0-100)
        """
        if df.empty or "close" not in df.columns or len(df) < 2:
            return 0.0
        
        # Calculate price changes
        price_changes = df["close"].pct_change().dropna()
        
        if len(price_changes) == 0:
            return 0.0
        
        # Check for extreme price changes
        max_change = self.config["max_price_change_percent"] / 100
        extreme_changes = (abs(price_changes) > max_change).sum()
        
        # Check for outliers using standard deviation
        std_threshold = self.config["outlier_std_threshold"]
        mean_change = price_changes.mean()
        std_change = price_changes.std()
        
        if std_change > 0:
            outliers = (abs(price_changes - mean_change) > std_threshold * std_change).sum()
        else:
            outliers = 0
        
        total_issues = extreme_changes + outliers
        consistency = ((len(price_changes) - total_issues) / len(price_changes)) * 100
        
        return min(100.0, max(0.0, consistency))
    
    def _check_timeliness(self, df: pd.DataFrame) -> float:
        """Check data timeliness (gaps in time series).
        
        Args:
            df: DataFrame to check
            
        Returns:
            Timeliness score (0-100)
        """
        if df.empty or len(df) < 2:
            return 0.0
        
        # Check for gaps in the time series
        time_diffs = df.index.to_series().diff().dropna()
        
        if len(time_diffs) == 0:
            return 0.0
        
        # Expected frequency (assume daily data)
        expected_freq = pd.Timedelta(days=1)
        max_gap = pd.Timedelta(hours=self.config["max_gap_hours"])
        
        # Count gaps that are too large
        large_gaps = (time_diffs > max_gap).sum()
        
        timeliness = ((len(time_diffs) - large_gaps) / len(time_diffs)) * 100
        return min(100.0, max(0.0, timeliness))
    
    def _check_validity(self, df: pd.DataFrame) -> float:
        """Check data validity (required columns, data types).
        
        Args:
            df: DataFrame to check
            
        Returns:
            Validity score (0-100)
        """
        if df.empty:
            return 0.0
        
        validity_score = 0.0
        total_checks = 0
        
        # Check required columns
        required_cols = self.config["required_columns"]
        present_cols = sum(1 for col in required_cols if col in df.columns)
        validity_score += (present_cols / len(required_cols)) * 50
        total_checks += 50
        
        # Check data types (numeric columns should be numeric)
        numeric_cols = ["open", "high", "low", "close", "volume"]
        numeric_score = 0
        for col in numeric_cols:
            if col in df.columns:
                if pd.api.types.is_numeric_dtype(df[col]):
                    numeric_score += 1
        
        if len(numeric_cols) > 0:
            validity_score += (numeric_score / len(numeric_cols)) * 30
            total_checks += 30
        
        # Check minimum data points
        min_points = self.config["min_data_points"]
        if len(df) >= min_points:
            validity_score += 20
        total_checks += 20
        
        return (validity_score / total_checks) * 100 if total_checks > 0 else 0.0
    
    def detect_anomalies(self, df: pd.DataFrame) -> pd.DataFrame:
        """Detect anomalies in the data.
        
        Args:
            df: DataFrame to analyze
            
        Returns:
            DataFrame with anomaly information
        """
        if df.empty or "close" not in df.columns:
            return pd.DataFrame()
        
        anomalies = []
        
        # Price-based anomalies
        price_changes = df["close"].pct_change()
        max_change = self.config["max_price_change_percent"] / 100
        
        extreme_changes = abs(price_changes) > max_change
        for idx in df.index[extreme_changes]:
            if pd.notna(price_changes.loc[idx]):
                anomalies.append({
                    "date": idx,
                    "type": "extreme_price_change",
                    "value": price_changes.loc[idx],
                    "threshold": max_change,
                    "severity": "high" if abs(price_changes.loc[idx]) > max_change * 2 else "medium"
                })
        
        # Volume anomalies
        if "volume" in df.columns:
            volume_mean = df["volume"].mean()
            volume_std = df["volume"].std()
            
            if volume_std > 0:
                volume_z_scores = abs((df["volume"] - volume_mean) / volume_std)
                volume_outliers = volume_z_scores > self.config["outlier_std_threshold"]
                
                for idx in df.index[volume_outliers]:
                    anomalies.append({
                        "date": idx,
                        "type": "volume_outlier",
                        "value": df.loc[idx, "volume"],
                        "z_score": volume_z_scores.loc[idx],
                        "severity": "medium"
                    })
        
        # OHLC inconsistencies
        for idx, row in df.iterrows():
            if not all(col in row.index for col in ["open", "high", "low", "close"]):
                continue
                
            if not (row["low"] <= row["open"] <= row["high"] and 
                   row["low"] <= row["close"] <= row["high"]):
                anomalies.append({
                    "date": idx,
                    "type": "ohlc_inconsistency",
                    "value": f"O:{row['open']}, H:{row['high']}, L:{row['low']}, C:{row['close']}",
                    "severity": "high"
                })
        
        return pd.DataFrame(anomalies)
    
    def cross_validate_providers(
        self, 
        provider_data: Dict[str, pd.DataFrame],
        max_deviation_percent: float = 5.0
    ) -> Dict[str, float]:
        """Cross-validate data across multiple providers.
        
        Args:
            provider_data: Dictionary of provider DataFrames
            max_deviation_percent: Maximum allowed price deviation
            
        Returns:
            Dictionary with validation scores for each provider
        """
        if len(provider_data) < 2:
            return {list(provider_data.keys())[0]: 100.0} if provider_data else {}
        
        scores = {}
        
        # Find common date range
        common_dates = None
        for provider, data in provider_data.items():
            if len(data) > 0:
                dates = set(data.index)
                if common_dates is None:
                    common_dates = dates
                else:
                    common_dates = common_dates.intersection(dates)
        
        if not common_dates:
            return {provider: 0.0 for provider in provider_data.keys()}
        
        # Calculate consensus prices
        consensus_prices = {}
        for date in common_dates:
            prices = []
            for provider, data in provider_data.items():
                if date in data.index and "close" in data.columns:
                    prices.append(data.loc[date, "close"])
            
            if len(prices) >= 2:
                consensus_prices[date] = np.median(prices)
        
        # Score each provider against consensus
        for provider, data in provider_data.items():
            if len(data) == 0:
                scores[provider] = 0.0
                continue
            
            deviations = []
            for date in common_dates:
                if date in data.index and date in consensus_prices:
                    actual_price = data.loc[date, "close"]
                    consensus_price = consensus_prices[date]
                    
                    if consensus_price > 0:
                        deviation = abs((actual_price - consensus_price) / consensus_price) * 100
                        deviations.append(deviation)
            
            if deviations:
                avg_deviation = np.mean(deviations)
                # Score inversely related to deviation
                score = max(0, 100 - (avg_deviation / max_deviation_percent) * 100)
                scores[provider] = min(100.0, score)
            else:
                scores[provider] = 0.0
        
        return scores
