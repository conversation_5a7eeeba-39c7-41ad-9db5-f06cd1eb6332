# ============================================================================
# 🚀 FIXED Bitcoin LSTM Training for Google Colab
# ============================================================================
# Copy this entire code into a new Google Colab notebook and run!
# Make sure to enable GPU: Runtime → Change runtime type → GPU

# Check GPU and install packages
import tensorflow as tf
print("🔍 GPU Available:", tf.config.list_physical_devices('GPU'))

# Enable GPU memory growth
gpus = tf.config.experimental.list_physical_devices('GPU')
if gpus:
    for gpu in gpus:
        tf.config.experimental.set_memory_growth(gpu, True)

# Install required packages
import subprocess
import sys

def install_package(package):
    subprocess.check_call([sys.executable, "-m", "pip", "install", "-q", package])

packages = ['yfinance', 'scikit-learn', 'matplotlib', 'seaborn']
for package in packages:
    try:
        __import__(package.replace('-', '_'))
    except ImportError:
        print(f"Installing {package}...")
        install_package(package)

# Import all required libraries
import yfinance as yf
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from datetime import datetime, timedelta
from sklearn.preprocessing import MinMaxScaler, StandardScaler
from sklearn.metrics import mean_squared_error, mean_absolute_error
import warnings
import os
import json
warnings.filterwarnings('ignore')

# Create directories
os.makedirs('results', exist_ok=True)

print("✅ Setup complete!")

# ============================================================================
# DATA COLLECTION
# ============================================================================

print("\n📊 Downloading Bitcoin data...")

# Download Bitcoin data with error handling
try:
    btc = yf.download('BTC-USD', start='2022-01-01', end=datetime.now().strftime('%Y-%m-%d'))
    
    # Handle multi-level columns from yfinance
    if hasattr(btc.columns, 'levels'):  # MultiIndex columns
        btc.columns = [col[0].lower() if isinstance(col, tuple) else str(col).lower() for col in btc.columns]
    else:
        btc.columns = [str(col).lower() for col in btc.columns]
    
    # Reset index to get Date as column
    btc.reset_index(inplace=True)
    
    # Ensure Date column exists
    date_cols = [col for col in btc.columns if 'date' in col.lower()]
    if date_cols:
        btc.rename(columns={date_cols[0]: 'Date'}, inplace=True)
    elif 'Date' not in btc.columns:
        btc['Date'] = btc.index
    
    print(f"✅ Downloaded {len(btc)} days of Bitcoin data")
    print(f"📅 Date range: {btc['Date'].min()} to {btc['Date'].max()}")
    print(f"💰 Price range: ${btc['close'].min():.2f} to ${btc['close'].max():.2f}")
    print(f"📋 Columns: {list(btc.columns)}")
    
except Exception as e:
    print(f"❌ Error downloading data: {e}")
    print("Using sample data instead...")
    # Create sample data as fallback
    dates = pd.date_range(start='2022-01-01', end=datetime.now(), freq='D')
    np.random.seed(42)
    prices = 30000 + np.cumsum(np.random.randn(len(dates)) * 100)
    btc = pd.DataFrame({
        'Date': dates,
        'open': prices * (1 + np.random.randn(len(dates)) * 0.01),
        'high': prices * (1 + np.abs(np.random.randn(len(dates))) * 0.02),
        'low': prices * (1 - np.abs(np.random.randn(len(dates))) * 0.02),
        'close': prices,
        'volume': np.random.randint(1000000, 10000000, len(dates))
    })

# ============================================================================
# TECHNICAL INDICATORS
# ============================================================================

def add_technical_indicators(df):
    """Add technical indicators with error handling"""
    df = df.copy()
    
    try:
        # Simple Moving Averages
        df['sma_20'] = df['close'].rolling(window=20, min_periods=1).mean()
        df['sma_50'] = df['close'].rolling(window=50, min_periods=1).mean()
        
        # Exponential Moving Averages
        df['ema_12'] = df['close'].ewm(span=12, min_periods=1).mean()
        df['ema_26'] = df['close'].ewm(span=26, min_periods=1).mean()
        
        # RSI
        delta = df['close'].diff()
        gain = (delta.where(delta > 0, 0)).rolling(window=14, min_periods=1).mean()
        loss = (-delta.where(delta < 0, 0)).rolling(window=14, min_periods=1).mean()
        rs = gain / (loss + 1e-8)  # Add small value to avoid division by zero
        df['rsi'] = 100 - (100 / (1 + rs))
        
        # MACD
        df['macd'] = df['ema_12'] - df['ema_26']
        df['macd_signal'] = df['macd'].ewm(span=9, min_periods=1).mean()
        
        # Bollinger Bands
        bb_std = df['close'].rolling(window=20, min_periods=1).std()
        df['bb_upper'] = df['sma_20'] + (bb_std * 2)
        df['bb_lower'] = df['sma_20'] - (bb_std * 2)
        
        # Volume indicators
        df['volume_sma'] = df['volume'].rolling(window=20, min_periods=1).mean()
        
        print("✅ Technical indicators added successfully")
        
    except Exception as e:
        print(f"⚠️ Error adding indicators: {e}")
        # Add basic indicators as fallback
        df['sma_20'] = df['close'].rolling(window=20, min_periods=1).mean()
        df['rsi'] = 50  # Neutral RSI
        df['macd'] = 0
    
    return df

print("\n📈 Adding technical indicators...")
btc_processed = add_technical_indicators(btc)

# Remove any remaining NaN values
btc_processed = btc_processed.fillna(method='ffill').fillna(method='bfill')
print(f"📊 Final dataset: {len(btc_processed)} rows, {len(btc_processed.columns)} columns")

# ============================================================================
# LSTM MODEL (SIMPLIFIED FOR COLAB)
# ============================================================================

class SimpleLSTM:
    def __init__(self, sequence_length=30):  # Shorter sequence for faster training
        self.sequence_length = sequence_length
        self.features = ['open', 'high', 'low', 'close', 'volume', 'sma_20', 'rsi', 'macd']
        self.model = None
        self.scaler = MinMaxScaler()
        self.feature_scaler = StandardScaler()
        
    def prepare_data(self, data):
        # Select available features
        available_features = [col for col in self.features if col in data.columns]
        print(f"Using features: {available_features}")
        
        feature_data = data[available_features].fillna(method='ffill')
        target_data = data['close'].fillna(method='ffill')
        
        # Scale data
        scaled_features = self.feature_scaler.fit_transform(feature_data)
        scaled_target = self.scaler.fit_transform(target_data.values.reshape(-1, 1)).flatten()
        
        # Create sequences
        X, y = [], []
        for i in range(self.sequence_length, len(scaled_features)):
            X.append(scaled_features[i-self.sequence_length:i])
            y.append(scaled_target[i])
        
        X, y = np.array(X), np.array(y)
        
        # Split data
        split_idx = int(len(X) * 0.8)
        return X[:split_idx], X[split_idx:], y[:split_idx], y[split_idx:]
    
    def build_model(self, input_shape):
        model = tf.keras.Sequential([
            tf.keras.layers.LSTM(64, return_sequences=True, input_shape=input_shape),
            tf.keras.layers.Dropout(0.2),
            tf.keras.layers.LSTM(32),
            tf.keras.layers.Dropout(0.2),
            tf.keras.layers.Dense(25, activation='relu'),
            tf.keras.layers.Dense(1)
        ])
        
        model.compile(
            optimizer=tf.keras.optimizers.Adam(learning_rate=0.001),
            loss='mse',
            metrics=['mae']
        )
        
        return model
    
    def train(self, X_train, y_train, X_val, y_val):
        input_shape = (X_train.shape[1], X_train.shape[2])
        self.model = self.build_model(input_shape)
        
        callbacks = [
            tf.keras.callbacks.EarlyStopping(patience=10, restore_best_weights=True),
            tf.keras.callbacks.ReduceLROnPlateau(factor=0.5, patience=5)
        ]
        
        history = self.model.fit(
            X_train, y_train,
            batch_size=32,  # Smaller batch size
            epochs=50,      # Fewer epochs for faster training
            validation_data=(X_val, y_val),
            callbacks=callbacks,
            verbose=1
        )
        
        return history
    
    def predict(self, X):
        predictions = self.model.predict(X, verbose=0)
        return self.scaler.inverse_transform(predictions).flatten()
    
    def evaluate(self, X_test, y_test):
        predictions = self.predict(X_test)
        y_test_rescaled = self.scaler.inverse_transform(y_test.reshape(-1, 1)).flatten()
        
        mse = mean_squared_error(y_test_rescaled, predictions)
        mae = mean_absolute_error(y_test_rescaled, predictions)
        rmse = np.sqrt(mse)
        mape = np.mean(np.abs((y_test_rescaled - predictions) / y_test_rescaled)) * 100
        
        return {
            'mse': mse, 'mae': mae, 'rmse': rmse, 'mape': mape,
            'predictions': predictions, 'actual': y_test_rescaled
        }

# ============================================================================
# TRAINING
# ============================================================================

print("\n🚀 Starting LSTM training...")

# Initialize and train model
lstm_model = SimpleLSTM(sequence_length=30)
X_train, X_test, y_train, y_test = lstm_model.prepare_data(btc_processed)

print(f"Training data: X={X_train.shape}, y={y_train.shape}")
print(f"Test data: X={X_test.shape}, y={y_test.shape}")

start_time = datetime.now()
history = lstm_model.train(X_train, y_train, X_test, y_test)
training_time = datetime.now() - start_time

print(f"⏱️ Training completed in: {training_time}")

# ============================================================================
# EVALUATION
# ============================================================================

print("\n📈 Evaluating model...")
metrics = lstm_model.evaluate(X_test, y_test)

print(f"\n{'='*50}")
print("🎯 MODEL PERFORMANCE")
print(f"{'='*50}")
print(f"RMSE: ${metrics['rmse']:.2f}")
print(f"MAE: ${metrics['mae']:.2f}")
print(f"MAPE: {metrics['mape']:.2f}%")

# Plot results
plt.figure(figsize=(15, 10))

# Training history
plt.subplot(2, 2, 1)
plt.plot(history.history['loss'], label='Training Loss')
plt.plot(history.history['val_loss'], label='Validation Loss')
plt.title('Model Loss')
plt.legend()
plt.grid(True)

# MAE history
plt.subplot(2, 2, 2)
plt.plot(history.history['mae'], label='Training MAE')
plt.plot(history.history['val_mae'], label='Validation MAE')
plt.title('Model MAE')
plt.legend()
plt.grid(True)

# Predictions vs Actual
plt.subplot(2, 2, 3)
n_points = min(100, len(metrics['actual']))
actual = metrics['actual'][-n_points:]
predicted = metrics['predictions'][-n_points:]

plt.plot(actual, label='Actual Price', alpha=0.7)
plt.plot(predicted, label='Predicted Price', alpha=0.7)
plt.title(f'Price Prediction - Last {n_points} Days')
plt.legend()
plt.grid(True)

# Scatter plot
plt.subplot(2, 2, 4)
plt.scatter(metrics['actual'], metrics['predictions'], alpha=0.5)
plt.plot([metrics['actual'].min(), metrics['actual'].max()], 
         [metrics['actual'].min(), metrics['actual'].max()], 'r--')
plt.xlabel('Actual Price')
plt.ylabel('Predicted Price')
plt.title('Actual vs Predicted')
plt.grid(True)

plt.tight_layout()
plt.savefig('results/colab_training_results.png', dpi=300, bbox_inches='tight')
plt.show()

# ============================================================================
# SAVE RESULTS
# ============================================================================

timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")

# Save model
model_path = f'results/colab_bitcoin_lstm_{timestamp}.h5'
lstm_model.model.save(model_path)

# Save results
results = {
    'timestamp': timestamp,
    'platform': 'Google Colab',
    'training_time_seconds': training_time.total_seconds(),
    'performance': {
        'rmse': float(metrics['rmse']),
        'mae': float(metrics['mae']),
        'mape': float(metrics['mape'])
    },
    'data_info': {
        'num_days': len(btc_processed),
        'features_used': lstm_model.features
    }
}

results_file = f'results/colab_results_{timestamp}.json'
with open(results_file, 'w') as f:
    json.dump(results, f, indent=2)

print(f"\n✅ TRAINING COMPLETED SUCCESSFULLY!")
print(f"📁 Model saved: {model_path}")
print(f"📊 Results saved: {results_file}")
print(f"📈 Chart saved: results/colab_training_results.png")

print(f"\n🎯 Final Performance:")
print(f"  • RMSE: ${metrics['rmse']:.2f}")
print(f"  • MAE: ${metrics['mae']:.2f}")
print(f"  • MAPE: {metrics['mape']:.2f}%")
print(f"  • Training Time: {training_time}")

print(f"\n📥 Download your files from the 'results' folder!")
print(f"🎉 Happy trading! 📈")

# List all files
print(f"\n📋 Generated files:")
import os
for file in os.listdir('results'):
    print(f"  • {file}")
