# ============================================================================
# 🔧 QUICK FIX for Learning Rate Schedule Error
# ============================================================================
# Run this cell if you encounter the learning rate schedule error

print("🔧 Applying quick fix for learning rate schedule error...")

# Fixed version of the build_robust_model method
def build_robust_model_fixed(self, input_shape):
    """Build robust LSTM with proper regularization - FIXED VERSION"""
    print(f"🏗️ Building robust LSTM architecture (FIXED)...")
    
    model = tf.keras.Sequential([
        # First LSTM layer with L2 regularization
        tf.keras.layers.LSTM(
            128, 
            return_sequences=True, 
            input_shape=input_shape,
            kernel_regularizer=tf.keras.regularizers.l2(0.001),
            recurrent_regularizer=tf.keras.regularizers.l2(0.001)
        ),
        tf.keras.layers.BatchNormalization(),
        tf.keras.layers.Dropout(0.3),
        
        # Second LSTM layer
        tf.keras.layers.LSTM(
            64, 
            return_sequences=True,
            kernel_regularizer=tf.keras.regularizers.l2(0.001),
            recurrent_regularizer=tf.keras.regularizers.l2(0.001)
        ),
        tf.keras.layers.BatchNormalization(),
        tf.keras.layers.Dropout(0.3),
        
        # Third LSTM layer
        tf.keras.layers.LSTM(
            32,
            kernel_regularizer=tf.keras.regularizers.l2(0.001),
            recurrent_regularizer=tf.keras.regularizers.l2(0.001)
        ),
        tf.keras.layers.BatchNormalization(),
        tf.keras.layers.Dropout(0.3),
        
        # Dense layers with regularization
        tf.keras.layers.Dense(
            50, 
            activation='relu',
            kernel_regularizer=tf.keras.regularizers.l2(0.001)
        ),
        tf.keras.layers.Dropout(0.2),
        
        tf.keras.layers.Dense(
            25, 
            activation='relu',
            kernel_regularizer=tf.keras.regularizers.l2(0.001)
        ),
        tf.keras.layers.Dropout(0.2),
        
        # Output layer
        tf.keras.layers.Dense(1)
    ])
    
    # Use FIXED learning rate (no schedule conflict)
    optimizer = tf.keras.optimizers.Adam(learning_rate=0.001)
    
    model.compile(
        optimizer=optimizer,
        loss='huber',  # More robust to outliers than MSE
        metrics=['mae', 'mse']
    )
    
    print(f"✅ Model built with {model.count_params():,} parameters (FIXED)")
    return model

# Fixed training function
def train_robust_lstm_fixed(lstm_model, X_train, y_train, X_val, y_val):
    """Train with proper callbacks and monitoring - FIXED VERSION"""
    print(f"\n🎯 Starting robust LSTM training (FIXED)...")
    print(f"⏰ Expected training time: 10-30 minutes (depending on GPU)")
    
    input_shape = (X_train.shape[1], X_train.shape[2])
    
    # Use the fixed model building method
    lstm_model.model = build_robust_model_fixed(lstm_model, input_shape)
    
    # Enhanced callbacks for better training (FIXED - no learning rate conflict)
    callbacks = [
        tf.keras.callbacks.EarlyStopping(
            monitor='val_loss',
            patience=20,  # More patience for better convergence
            restore_best_weights=True,
            verbose=1,
            min_delta=0.0001
        ),
        tf.keras.callbacks.ReduceLROnPlateau(
            monitor='val_loss',
            factor=0.5,
            patience=10,
            min_lr=1e-7,
            verbose=1
        ),
        tf.keras.callbacks.ModelCheckpoint(
            'results/best_model_checkpoint.h5',
            monitor='val_loss',
            save_best_only=True,
            verbose=1
        )
    ]
    
    # Train with proper batch size and epochs
    lstm_model.history = lstm_model.model.fit(
        X_train, y_train,
        batch_size=32,  # Reasonable batch size
        epochs=200,     # More epochs for proper training
        validation_data=(X_val, y_val),
        callbacks=callbacks,
        verbose=1,
        shuffle=True
    )
    
    return lstm_model.history

print("✅ Quick fix applied! Now run the training with:")
print("history = train_robust_lstm_fixed(lstm_model, X_train, y_train, X_val, y_val)")

# ============================================================================
# Alternative: Simplified Training (if still having issues)
# ============================================================================

def simple_robust_training(lstm_model, X_train, y_train, X_val, y_val):
    """Simplified robust training without complex scheduling"""
    print("🚀 Starting simplified robust training...")
    
    # Simple model architecture
    input_shape = (X_train.shape[1], X_train.shape[2])
    
    model = tf.keras.Sequential([
        tf.keras.layers.LSTM(64, return_sequences=True, input_shape=input_shape),
        tf.keras.layers.Dropout(0.2),
        tf.keras.layers.LSTM(32, return_sequences=True),
        tf.keras.layers.Dropout(0.2),
        tf.keras.layers.LSTM(16),
        tf.keras.layers.Dropout(0.2),
        tf.keras.layers.Dense(25, activation='relu'),
        tf.keras.layers.Dense(1)
    ])
    
    # Simple optimizer
    model.compile(
        optimizer=tf.keras.optimizers.Adam(learning_rate=0.001),
        loss='mse',
        metrics=['mae']
    )
    
    lstm_model.model = model
    
    # Simple callbacks
    callbacks = [
        tf.keras.callbacks.EarlyStopping(
            monitor='val_loss',
            patience=15,
            restore_best_weights=True,
            verbose=1
        )
    ]
    
    # Train
    history = model.fit(
        X_train, y_train,
        batch_size=32,
        epochs=100,
        validation_data=(X_val, y_val),
        callbacks=callbacks,
        verbose=1
    )
    
    lstm_model.history = history
    return history

print("\n💡 If you still have issues, use the simplified version:")
print("history = simple_robust_training(lstm_model, X_train, y_train, X_val, y_val)")
