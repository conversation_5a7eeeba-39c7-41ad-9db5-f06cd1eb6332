# ============================================================================
# 🚀 ROBUST Bitcoin LSTM Training - Proper Dataset & Training Time
# ============================================================================
# This version addresses the issues: too fast training, insufficient data, overfitting

import tensorflow as tf
import yfinance as yf
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
from datetime import datetime, timedelta
from sklearn.preprocessing import MinMaxScaler, StandardScaler
from sklearn.metrics import mean_squared_error, mean_absolute_error
import warnings
import os
import json
warnings.filterwarnings('ignore')

# GPU Setup
print("🔍 Checking GPU availability...")
gpus = tf.config.experimental.list_physical_devices('GPU')
if gpus:
    for gpu in gpus:
        tf.config.experimental.set_memory_growth(gpu, True)
    print(f"✅ GPU found: {len(gpus)} device(s)")
else:
    print("⚠️ No GPU found - using CPU (will be slower)")

# Create directories
os.makedirs('results', exist_ok=True)

# ============================================================================
# ENHANCED DATA COLLECTION - MORE COMPREHENSIVE DATASET
# ============================================================================

def collect_comprehensive_bitcoin_data():
    """Collect comprehensive Bitcoin data with multiple timeframes"""
    print("\n📊 Collecting comprehensive Bitcoin dataset...")
    
    # Get longer historical data (5+ years)
    start_date = '2019-01-01'  # Extended to 5+ years
    end_date = datetime.now().strftime('%Y-%m-%d')
    
    print(f"📅 Fetching data from {start_date} to {end_date}")
    
    # Download daily data
    btc_daily = yf.download('BTC-USD', start=start_date, end=end_date, interval='1d')
    
    # Handle MultiIndex columns
    if hasattr(btc_daily.columns, 'levels'):
        btc_daily.columns = [col[0].lower() for col in btc_daily.columns]
    else:
        btc_daily.columns = [col.lower() for col in btc_daily.columns]
    
    btc_daily.reset_index(inplace=True)
    btc_daily.rename(columns={'date': 'Date'}, inplace=True)
    
    print(f"✅ Collected {len(btc_daily)} days of Bitcoin data")
    print(f"📈 Price range: ${btc_daily['close'].min():.2f} to ${btc_daily['close'].max():.2f}")
    
    return btc_daily

def add_comprehensive_technical_indicators(df):
    """Add comprehensive technical indicators for better feature engineering"""
    print("📈 Adding comprehensive technical indicators...")
    df = df.copy()
    
    # Multiple Moving Averages
    for period in [5, 10, 20, 50, 100, 200]:
        df[f'sma_{period}'] = df['close'].rolling(window=period, min_periods=1).mean()
    
    # Multiple EMAs
    for period in [5, 12, 21, 26, 50]:
        df[f'ema_{period}'] = df['close'].ewm(span=period, min_periods=1).mean()
    
    # RSI with multiple periods
    for period in [14, 21]:
        delta = df['close'].diff()
        gain = (delta.where(delta > 0, 0)).rolling(window=period, min_periods=1).mean()
        loss = (-delta.where(delta < 0, 0)).rolling(window=period, min_periods=1).mean()
        rs = gain / (loss + 1e-8)
        df[f'rsi_{period}'] = 100 - (100 / (1 + rs))
    
    # MACD variations
    df['macd_12_26'] = df['ema_12'] - df['ema_26']
    df['macd_signal_12_26'] = df['macd_12_26'].ewm(span=9, min_periods=1).mean()
    df['macd_histogram'] = df['macd_12_26'] - df['macd_signal_12_26']
    
    # Bollinger Bands with multiple periods
    for period in [20, 50]:
        sma = df['close'].rolling(window=period, min_periods=1).mean()
        std = df['close'].rolling(window=period, min_periods=1).std()
        df[f'bb_upper_{period}'] = sma + (std * 2)
        df[f'bb_lower_{period}'] = sma - (std * 2)
        df[f'bb_width_{period}'] = df[f'bb_upper_{period}'] - df[f'bb_lower_{period}']
        df[f'bb_position_{period}'] = (df['close'] - df[f'bb_lower_{period}']) / df[f'bb_width_{period}']
    
    # Stochastic Oscillator
    for period in [14, 21]:
        low_min = df['low'].rolling(window=period, min_periods=1).min()
        high_max = df['high'].rolling(window=period, min_periods=1).max()
        df[f'stoch_k_{period}'] = 100 * ((df['close'] - low_min) / (high_max - low_min + 1e-8))
        df[f'stoch_d_{period}'] = df[f'stoch_k_{period}'].rolling(window=3, min_periods=1).mean()
    
    # Average True Range
    high_low = df['high'] - df['low']
    high_close = np.abs(df['high'] - df['close'].shift())
    low_close = np.abs(df['low'] - df['close'].shift())
    ranges = pd.concat([high_low, high_close, low_close], axis=1)
    true_range = ranges.max(axis=1)
    df['atr'] = true_range.rolling(window=14, min_periods=1).mean()
    
    # Volume indicators
    df['volume_sma_20'] = df['volume'].rolling(window=20, min_periods=1).mean()
    df['volume_ratio'] = df['volume'] / df['volume_sma_20']
    df['volume_roc'] = df['volume'].pct_change(periods=5)
    
    # Price momentum and volatility
    for period in [5, 10, 20]:
        df[f'momentum_{period}'] = df['close'] / df['close'].shift(period) - 1
        df[f'volatility_{period}'] = df['close'].rolling(window=period, min_periods=1).std()
    
    # Rate of Change
    for period in [5, 10, 20]:
        df[f'roc_{period}'] = df['close'].pct_change(periods=period)
    
    # Williams %R
    for period in [14, 21]:
        high_max = df['high'].rolling(window=period, min_periods=1).max()
        low_min = df['low'].rolling(window=period, min_periods=1).min()
        df[f'williams_r_{period}'] = -100 * ((high_max - df['close']) / (high_max - low_min + 1e-8))
    
    print(f"✅ Added comprehensive technical indicators")
    print(f"📊 Total features: {len(df.columns)}")
    
    return df

# ============================================================================
# ROBUST LSTM MODEL WITH PROPER ARCHITECTURE
# ============================================================================

class RobustBitcoinLSTM:
    """Enhanced LSTM with proper regularization and architecture"""
    
    def __init__(self, sequence_length=90):  # Longer sequences for better patterns
        self.sequence_length = sequence_length
        self.model = None
        self.scaler = MinMaxScaler(feature_range=(0, 1))
        self.feature_scaler = StandardScaler()
        self.history = None
        
        # Select most important features (avoid overfitting)
        self.features = [
            'open', 'high', 'low', 'close', 'volume',
            'sma_5', 'sma_20', 'sma_50', 'sma_200',
            'ema_12', 'ema_26', 'ema_50',
            'rsi_14', 'rsi_21',
            'macd_12_26', 'macd_signal_12_26', 'macd_histogram',
            'bb_upper_20', 'bb_lower_20', 'bb_position_20',
            'stoch_k_14', 'stoch_d_14',
            'atr', 'volume_ratio',
            'momentum_10', 'momentum_20',
            'volatility_20', 'roc_10',
            'williams_r_14'
        ]
        
    def prepare_data(self, data):
        """Enhanced data preparation with better validation"""
        print(f"\n📊 Preparing data for robust LSTM training...")
        
        # Select available features
        available_features = [col for col in self.features if col in data.columns]
        missing_features = [col for col in self.features if col not in data.columns]
        
        if missing_features:
            print(f"⚠️ Missing features: {missing_features}")
        
        print(f"✅ Using {len(available_features)} features")
        self.feature_columns = available_features
        
        # Prepare feature matrix and target
        feature_data = data[available_features].fillna(method='ffill').fillna(method='bfill')
        target_data = data['close'].fillna(method='ffill').fillna(method='bfill')
        
        # Scale data
        scaled_features = self.feature_scaler.fit_transform(feature_data)
        scaled_target = self.scaler.fit_transform(target_data.values.reshape(-1, 1)).flatten()
        
        # Create sequences
        X, y = [], []
        for i in range(self.sequence_length, len(scaled_features)):
            X.append(scaled_features[i-self.sequence_length:i])
            y.append(scaled_target[i])
        
        X, y = np.array(X), np.array(y)
        
        # Better train/validation/test split (70/15/15)
        train_size = int(len(X) * 0.70)
        val_size = int(len(X) * 0.15)
        
        X_train = X[:train_size]
        X_val = X[train_size:train_size + val_size]
        X_test = X[train_size + val_size:]
        
        y_train = y[:train_size]
        y_val = y[train_size:train_size + val_size]
        y_test = y[train_size + val_size:]
        
        print(f"📈 Data split:")
        print(f"  Training: {X_train.shape[0]} samples")
        print(f"  Validation: {X_val.shape[0]} samples")
        print(f"  Test: {X_test.shape[0]} samples")
        
        return X_train, X_val, X_test, y_train, y_val, y_test
    
    def build_robust_model(self, input_shape):
        """Build robust LSTM with proper regularization"""
        print(f"🏗️ Building robust LSTM architecture...")
        
        model = tf.keras.Sequential([
            # First LSTM layer with L2 regularization
            tf.keras.layers.LSTM(
                128, 
                return_sequences=True, 
                input_shape=input_shape,
                kernel_regularizer=tf.keras.regularizers.l2(0.001),
                recurrent_regularizer=tf.keras.regularizers.l2(0.001)
            ),
            tf.keras.layers.BatchNormalization(),
            tf.keras.layers.Dropout(0.3),
            
            # Second LSTM layer
            tf.keras.layers.LSTM(
                64, 
                return_sequences=True,
                kernel_regularizer=tf.keras.regularizers.l2(0.001),
                recurrent_regularizer=tf.keras.regularizers.l2(0.001)
            ),
            tf.keras.layers.BatchNormalization(),
            tf.keras.layers.Dropout(0.3),
            
            # Third LSTM layer
            tf.keras.layers.LSTM(
                32,
                kernel_regularizer=tf.keras.regularizers.l2(0.001),
                recurrent_regularizer=tf.keras.regularizers.l2(0.001)
            ),
            tf.keras.layers.BatchNormalization(),
            tf.keras.layers.Dropout(0.3),
            
            # Dense layers with regularization
            tf.keras.layers.Dense(
                50, 
                activation='relu',
                kernel_regularizer=tf.keras.regularizers.l2(0.001)
            ),
            tf.keras.layers.Dropout(0.2),
            
            tf.keras.layers.Dense(
                25, 
                activation='relu',
                kernel_regularizer=tf.keras.regularizers.l2(0.001)
            ),
            tf.keras.layers.Dropout(0.2),
            
            # Output layer
            tf.keras.layers.Dense(1)
        ])
        
        # Use custom learning rate schedule
        initial_learning_rate = 0.001
        lr_schedule = tf.keras.optimizers.schedules.ExponentialDecay(
            initial_learning_rate,
            decay_steps=100,
            decay_rate=0.96,
            staircase=True
        )
        
        optimizer = tf.keras.optimizers.Adam(learning_rate=lr_schedule)
        
        model.compile(
            optimizer=optimizer,
            loss='huber',  # More robust to outliers than MSE
            metrics=['mae', 'mse']
        )
        
        print(f"✅ Model built with {model.count_params():,} parameters")
        return model
    
    def train_robust(self, X_train, y_train, X_val, y_val):
        """Train with proper callbacks and monitoring"""
        print(f"\n🎯 Starting robust LSTM training...")
        
        input_shape = (X_train.shape[1], X_train.shape[2])
        self.model = self.build_robust_model(input_shape)
        
        # Enhanced callbacks for better training
        callbacks = [
            tf.keras.callbacks.EarlyStopping(
                monitor='val_loss',
                patience=20,  # More patience for better convergence
                restore_best_weights=True,
                verbose=1,
                min_delta=0.0001
            ),
            tf.keras.callbacks.ReduceLROnPlateau(
                monitor='val_loss',
                factor=0.5,
                patience=10,
                min_lr=1e-7,
                verbose=1
            ),
            tf.keras.callbacks.ModelCheckpoint(
                'results/best_model_checkpoint.h5',
                monitor='val_loss',
                save_best_only=True,
                verbose=1
            )
        ]
        
        # Train with proper batch size and epochs
        self.history = self.model.fit(
            X_train, y_train,
            batch_size=32,  # Reasonable batch size
            epochs=200,     # More epochs for proper training
            validation_data=(X_val, y_val),
            callbacks=callbacks,
            verbose=1,
            shuffle=True
        )
        
        return self.history
    
    def predict(self, X):
        """Make predictions with proper scaling"""
        predictions = self.model.predict(X, verbose=0)
        return self.scaler.inverse_transform(predictions).flatten()
    
    def comprehensive_evaluate(self, X_test, y_test):
        """Comprehensive model evaluation"""
        print(f"\n📈 Comprehensive model evaluation...")
        
        predictions = self.predict(X_test)
        y_test_rescaled = self.scaler.inverse_transform(y_test.reshape(-1, 1)).flatten()
        
        # Calculate comprehensive metrics
        mse = mean_squared_error(y_test_rescaled, predictions)
        mae = mean_absolute_error(y_test_rescaled, predictions)
        rmse = np.sqrt(mse)
        mape = np.mean(np.abs((y_test_rescaled - predictions) / y_test_rescaled)) * 100
        
        # Directional accuracy (important for trading)
        actual_direction = np.diff(y_test_rescaled) > 0
        pred_direction = np.diff(predictions) > 0
        directional_accuracy = np.mean(actual_direction == pred_direction) * 100
        
        # R-squared
        ss_res = np.sum((y_test_rescaled - predictions) ** 2)
        ss_tot = np.sum((y_test_rescaled - np.mean(y_test_rescaled)) ** 2)
        r2_score = 1 - (ss_res / ss_tot)
        
        return {
            'mse': mse, 'mae': mae, 'rmse': rmse, 'mape': mape,
            'directional_accuracy': directional_accuracy,
            'r2_score': r2_score,
            'predictions': predictions, 'actual': y_test_rescaled
        }

# ============================================================================
# MAIN EXECUTION
# ============================================================================

def main():
    print("🚀 ROBUST Bitcoin LSTM Training - Comprehensive Dataset")
    print("=" * 70)
    
    # Collect comprehensive data
    btc_data = collect_comprehensive_bitcoin_data()
    
    # Add comprehensive technical indicators
    btc_processed = add_comprehensive_technical_indicators(btc_data)
    
    # Remove any remaining NaN values
    btc_processed = btc_processed.fillna(method='ffill').fillna(method='bfill')
    
    print(f"\n📊 Final dataset summary:")
    print(f"  • Total days: {len(btc_processed)}")
    print(f"  • Total features: {len(btc_processed.columns)}")
    print(f"  • Date range: {btc_processed['Date'].min()} to {btc_processed['Date'].max()}")
    print(f"  • Price range: ${btc_processed['close'].min():.2f} to ${btc_processed['close'].max():.2f}")
    
    # Initialize robust LSTM
    lstm_model = RobustBitcoinLSTM(sequence_length=90)
    
    # Prepare data
    X_train, X_val, X_test, y_train, y_val, y_test = lstm_model.prepare_data(btc_processed)
    
    # Train model
    start_time = datetime.now()
    history = lstm_model.train_robust(X_train, y_train, X_val, y_val)
    training_time = datetime.now() - start_time
    
    print(f"\n⏱️ Training completed in: {training_time}")
    
    # Comprehensive evaluation
    metrics = lstm_model.comprehensive_evaluate(X_test, y_test)
    
    # Display results
    print(f"\n{'='*70}")
    print("🎯 ROBUST LSTM MODEL PERFORMANCE")
    print(f"{'='*70}")
    print(f"RMSE: ${metrics['rmse']:.2f}")
    print(f"MAE: ${metrics['mae']:.2f}")
    print(f"MAPE: {metrics['mape']:.2f}%")
    print(f"Directional Accuracy: {metrics['directional_accuracy']:.2f}%")
    print(f"R² Score: {metrics['r2_score']:.4f}")
    print(f"Training Time: {training_time}")
    print(f"Epochs Trained: {len(history.history['loss'])}")
    
    # Save comprehensive results
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    
    results = {
        'timestamp': timestamp,
        'training_type': 'robust_comprehensive',
        'training_time_seconds': training_time.total_seconds(),
        'data_info': {
            'num_days': len(btc_processed),
            'num_features': len(lstm_model.feature_columns),
            'date_range': f"{btc_processed['Date'].min()} to {btc_processed['Date'].max()}",
            'sequence_length': lstm_model.sequence_length
        },
        'performance_metrics': {
            'rmse': float(metrics['rmse']),
            'mae': float(metrics['mae']),
            'mape': float(metrics['mape']),
            'directional_accuracy': float(metrics['directional_accuracy']),
            'r2_score': float(metrics['r2_score'])
        },
        'training_summary': {
            'epochs_trained': len(history.history['loss']),
            'final_loss': float(history.history['loss'][-1]),
            'final_val_loss': float(history.history['val_loss'][-1]),
            'best_val_loss': float(min(history.history['val_loss']))
        }
    }
    
    # Save results
    results_file = f'results/robust_lstm_results_{timestamp}.json'
    with open(results_file, 'w') as f:
        json.dump(results, f, indent=2)
    
    # Save model
    model_file = f'results/robust_bitcoin_lstm_{timestamp}.h5'
    lstm_model.model.save(model_file)
    
    print(f"\n✅ ROBUST TRAINING COMPLETED!")
    print(f"📁 Model saved: {model_file}")
    print(f"📊 Results saved: {results_file}")
    print(f"🎯 This training should take 10-30 minutes for proper convergence")

if __name__ == "__main__":
    main()
