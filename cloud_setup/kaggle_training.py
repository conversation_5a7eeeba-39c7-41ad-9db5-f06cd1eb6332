#!/usr/bin/env python3
"""
Bitcoin LSTM Training for Kaggle GPU Environment

This script is optimized for Kaggle's GPU environment with 30 hours/week free GPU time.
Simply copy this code into a new Kaggle notebook and run.
"""

# =============================================================================
# KAGGLE SETUP - Enable GPU in notebook settings first!
# =============================================================================

import os
import sys
import pandas as pd
import numpy as np
import tensorflow as tf
from datetime import datetime, timedelta
import json
import matplotlib.pyplot as plt
import seaborn as sns
from sklearn.preprocessing import MinMaxScaler, StandardScaler
from sklearn.metrics import mean_squared_error, mean_absolute_error
import warnings
warnings.filterwarnings('ignore')

# Check GPU availability
print("🔍 Checking GPU availability...")
gpus = tf.config.experimental.list_physical_devices('GPU')
if gpus:
    print(f"✅ GPU found: {gpus}")
    # Enable memory growth
    for gpu in gpus:
        tf.config.experimental.set_memory_growth(gpu, True)
else:
    print("⚠️ No GPU found - using CPU")

print(f"TensorFlow version: {tf.__version__}")

# =============================================================================
# DATA COLLECTION
# =============================================================================

print("\n📊 Collecting Bitcoin data...")

# Install yfinance if not available
try:
    import yfinance as yf
except ImportError:
    os.system('pip install yfinance')
    import yfinance as yf

# Download Bitcoin data
btc = yf.download('BTC-USD', start='2022-01-01', end=datetime.now().strftime('%Y-%m-%d'))
btc.columns = [col.lower() for col in btc.columns]
btc.reset_index(inplace=True)
btc.rename(columns={'date': 'Date'}, inplace=True)

print(f"Downloaded {len(btc)} days of Bitcoin data")
print(f"Date range: {btc['Date'].min()} to {btc['Date'].max()}")
print(f"Price range: ${btc['close'].min():.2f} to ${btc['close'].max():.2f}")

# =============================================================================
# TECHNICAL INDICATORS
# =============================================================================

def add_technical_indicators(df):
    """Add comprehensive technical indicators"""
    df = df.copy()
    
    # Moving Averages
    df['sma_20'] = df['close'].rolling(window=20).mean()
    df['sma_50'] = df['close'].rolling(window=50).mean()
    df['sma_200'] = df['close'].rolling(window=200).mean()
    df['ema_12'] = df['close'].ewm(span=12).mean()
    df['ema_26'] = df['close'].ewm(span=26).mean()
    
    # RSI
    delta = df['close'].diff()
    gain = (delta.where(delta > 0, 0)).rolling(window=14).mean()
    loss = (-delta.where(delta < 0, 0)).rolling(window=14).mean()
    rs = gain / loss
    df['rsi'] = 100 - (100 / (1 + rs))
    
    # MACD
    df['macd'] = df['ema_12'] - df['ema_26']
    df['macd_signal'] = df['macd'].ewm(span=9).mean()
    df['macd_histogram'] = df['macd'] - df['macd_signal']
    
    # Bollinger Bands
    bb_period = 20
    bb_std = df['close'].rolling(window=bb_period).std()
    df['bb_upper'] = df['sma_20'] + (bb_std * 2)
    df['bb_lower'] = df['sma_20'] - (bb_std * 2)
    df['bb_width'] = df['bb_upper'] - df['bb_lower']
    df['bb_position'] = (df['close'] - df['bb_lower']) / df['bb_width']
    
    # Average True Range (ATR)
    high_low = df['high'] - df['low']
    high_close = np.abs(df['high'] - df['close'].shift())
    low_close = np.abs(df['low'] - df['close'].shift())
    ranges = pd.concat([high_low, high_close, low_close], axis=1)
    true_range = ranges.max(axis=1)
    df['atr'] = true_range.rolling(window=14).mean()
    
    # Stochastic Oscillator
    low_14 = df['low'].rolling(window=14).min()
    high_14 = df['high'].rolling(window=14).max()
    df['stoch_k'] = 100 * ((df['close'] - low_14) / (high_14 - low_14))
    df['stoch_d'] = df['stoch_k'].rolling(window=3).mean()
    
    # Volume indicators
    df['volume_sma'] = df['volume'].rolling(window=20).mean()
    df['volume_ratio'] = df['volume'] / df['volume_sma']
    
    # Price momentum
    df['momentum'] = df['close'] / df['close'].shift(10) - 1
    df['roc'] = df['close'].pct_change(periods=12)
    
    return df

print("📈 Adding technical indicators...")
btc_processed = add_technical_indicators(btc)
btc_processed = btc_processed.dropna()

print(f"✅ Technical indicators added")
print(f"Final dataset: {len(btc_processed)} rows, {len(btc_processed.columns)} columns")

# =============================================================================
# LSTM MODEL IMPLEMENTATION
# =============================================================================

class KaggleBitcoinLSTM:
    """Optimized LSTM for Kaggle GPU environment"""
    
    def __init__(self, sequence_length=60):
        self.sequence_length = sequence_length
        self.features = [
            'open', 'high', 'low', 'close', 'volume',
            'sma_20', 'sma_50', 'sma_200', 'ema_12', 'ema_26',
            'rsi', 'macd', 'macd_signal', 'macd_histogram',
            'bb_upper', 'bb_lower', 'bb_width', 'bb_position',
            'atr', 'stoch_k', 'stoch_d', 'volume_ratio',
            'momentum', 'roc'
        ]
        self.model = None
        self.scaler = MinMaxScaler()
        self.feature_scaler = StandardScaler()
        self.history = None
        
    def prepare_data(self, data):
        """Prepare data for LSTM training"""
        # Select available features
        available_features = [col for col in self.features if col in data.columns]
        print(f"Using {len(available_features)} features")
        
        feature_data = data[available_features].fillna(method='ffill')
        target_data = data['close'].fillna(method='ffill')
        
        # Scale data
        scaled_features = self.feature_scaler.fit_transform(feature_data)
        scaled_target = self.scaler.fit_transform(target_data.values.reshape(-1, 1)).flatten()
        
        # Create sequences
        X, y = [], []
        for i in range(self.sequence_length, len(scaled_features)):
            X.append(scaled_features[i-self.sequence_length:i])
            y.append(scaled_target[i])
        
        X, y = np.array(X), np.array(y)
        
        # Split data (80% train, 20% test)
        split_idx = int(len(X) * 0.8)
        return X[:split_idx], X[split_idx:], y[:split_idx], y[split_idx:]
    
    def build_model(self, input_shape):
        """Build optimized LSTM architecture"""
        model = tf.keras.Sequential([
            # First LSTM layer
            tf.keras.layers.LSTM(128, return_sequences=True, input_shape=input_shape),
            tf.keras.layers.BatchNormalization(),
            tf.keras.layers.Dropout(0.3),
            
            # Second LSTM layer
            tf.keras.layers.LSTM(64, return_sequences=True),
            tf.keras.layers.BatchNormalization(),
            tf.keras.layers.Dropout(0.3),
            
            # Third LSTM layer
            tf.keras.layers.LSTM(32),
            tf.keras.layers.BatchNormalization(),
            tf.keras.layers.Dropout(0.3),
            
            # Dense layers
            tf.keras.layers.Dense(50, activation='relu'),
            tf.keras.layers.Dropout(0.2),
            tf.keras.layers.Dense(25, activation='relu'),
            tf.keras.layers.Dense(1)
        ])
        
        # Use mixed precision for faster training
        optimizer = tf.keras.optimizers.Adam(learning_rate=0.001)
        
        model.compile(
            optimizer=optimizer,
            loss='huber',  # More robust to outliers
            metrics=['mae', 'mse']
        )
        
        return model
    
    def train(self, X_train, y_train, X_val, y_val):
        """Train the model with advanced callbacks"""
        input_shape = (X_train.shape[1], X_train.shape[2])
        self.model = self.build_model(input_shape)
        
        print(f"Model built with {self.model.count_params():,} parameters")
        
        # Advanced callbacks
        callbacks = [
            tf.keras.callbacks.EarlyStopping(
                monitor='val_loss',
                patience=15,
                restore_best_weights=True,
                verbose=1
            ),
            tf.keras.callbacks.ReduceLROnPlateau(
                monitor='val_loss',
                factor=0.5,
                patience=7,
                min_lr=1e-7,
                verbose=1
            ),
            tf.keras.callbacks.ModelCheckpoint(
                'best_model.h5',
                monitor='val_loss',
                save_best_only=True,
                verbose=1
            )
        ]
        
        # Train model
        self.history = self.model.fit(
            X_train, y_train,
            batch_size=64,
            epochs=100,
            validation_data=(X_val, y_val),
            callbacks=callbacks,
            verbose=1
        )
        
        return self.history
    
    def predict(self, X):
        """Make predictions"""
        predictions = self.model.predict(X, verbose=0)
        return self.scaler.inverse_transform(predictions).flatten()
    
    def evaluate(self, X_test, y_test):
        """Comprehensive model evaluation"""
        predictions = self.predict(X_test)
        y_test_rescaled = self.scaler.inverse_transform(y_test.reshape(-1, 1)).flatten()
        
        # Calculate metrics
        mse = mean_squared_error(y_test_rescaled, predictions)
        mae = mean_absolute_error(y_test_rescaled, predictions)
        rmse = np.sqrt(mse)
        mape = np.mean(np.abs((y_test_rescaled - predictions) / y_test_rescaled)) * 100
        
        # Directional accuracy
        actual_direction = np.diff(y_test_rescaled) > 0
        pred_direction = np.diff(predictions) > 0
        directional_accuracy = np.mean(actual_direction == pred_direction) * 100
        
        return {
            'mse': mse, 'mae': mae, 'rmse': rmse, 'mape': mape,
            'directional_accuracy': directional_accuracy,
            'predictions': predictions, 'actual': y_test_rescaled
        }

# =============================================================================
# TRAINING EXECUTION
# =============================================================================

print("\n🚀 Starting LSTM training on Kaggle GPU...")

# Initialize model
lstm_model = KaggleBitcoinLSTM(sequence_length=60)

# Prepare data
print("📊 Preparing training data...")
X_train, X_test, y_train, y_test = lstm_model.prepare_data(btc_processed)

print(f"Training data shape: X={X_train.shape}, y={y_train.shape}")
print(f"Test data shape: X={X_test.shape}, y={y_test.shape}")

# Train model
print("🎯 Training LSTM model...")
start_time = datetime.now()
history = lstm_model.train(X_train, y_train, X_test, y_test)
training_time = datetime.now() - start_time

print(f"⏱️ Training completed in: {training_time}")

# =============================================================================
# EVALUATION AND VISUALIZATION
# =============================================================================

print("📈 Evaluating model performance...")
metrics = lstm_model.evaluate(X_test, y_test)

print(f"\n{'='*60}")
print("🎯 KAGGLE LSTM MODEL PERFORMANCE")
print(f"{'='*60}")
print(f"RMSE: ${metrics['rmse']:.2f}")
print(f"MAE: ${metrics['mae']:.2f}")
print(f"MAPE: {metrics['mape']:.2f}%")
print(f"Directional Accuracy: {metrics['directional_accuracy']:.2f}%")
print(f"Training Time: {training_time}")

# Plot training history
plt.figure(figsize=(15, 10))

# Loss plots
plt.subplot(2, 2, 1)
plt.plot(history.history['loss'], label='Training Loss')
plt.plot(history.history['val_loss'], label='Validation Loss')
plt.title('Model Loss')
plt.xlabel('Epoch')
plt.ylabel('Loss')
plt.legend()
plt.grid(True)

# MAE plots
plt.subplot(2, 2, 2)
plt.plot(history.history['mae'], label='Training MAE')
plt.plot(history.history['val_mae'], label='Validation MAE')
plt.title('Model MAE')
plt.xlabel('Epoch')
plt.ylabel('MAE')
plt.legend()
plt.grid(True)

# Predictions vs Actual
plt.subplot(2, 2, 3)
n_points = min(100, len(metrics['actual']))
actual = metrics['actual'][-n_points:]
predicted = metrics['predictions'][-n_points:]

plt.plot(actual, label='Actual Price', alpha=0.7)
plt.plot(predicted, label='Predicted Price', alpha=0.7)
plt.title(f'Price Prediction - Last {n_points} Days')
plt.xlabel('Time Steps')
plt.ylabel('Price ($)')
plt.legend()
plt.grid(True)

# Scatter plot
plt.subplot(2, 2, 4)
plt.scatter(metrics['actual'], metrics['predictions'], alpha=0.5)
plt.plot([metrics['actual'].min(), metrics['actual'].max()], 
         [metrics['actual'].min(), metrics['actual'].max()], 'r--')
plt.xlabel('Actual Price')
plt.ylabel('Predicted Price')
plt.title('Actual vs Predicted')
plt.grid(True)

correlation = np.corrcoef(metrics['actual'], metrics['predictions'])[0, 1]
plt.text(0.05, 0.95, f'R² = {correlation**2:.3f}', transform=plt.gca().transAxes,
         bbox=dict(boxstyle='round', facecolor='white', alpha=0.8))

plt.tight_layout()
plt.show()

# =============================================================================
# TRADING SIGNALS GENERATION
# =============================================================================

print("\n💹 Generating trading signals...")

# Use recent data for signal generation
recent_data = btc_processed.tail(120).copy()
available_features = [col for col in lstm_model.features if col in recent_data.columns]
feature_data = recent_data[available_features].fillna(method='ffill')
scaled_features = lstm_model.feature_scaler.transform(feature_data)

# Create sequences
X_pred = []
valid_indices = []

for i in range(lstm_model.sequence_length, len(scaled_features)):
    X_pred.append(scaled_features[i-lstm_model.sequence_length:i])
    valid_indices.append(i)

X_pred = np.array(X_pred)
predictions = lstm_model.predict(X_pred)

# Create signals
signals_df = recent_data.iloc[valid_indices].copy()
signals_df['predicted_price'] = predictions
signals_df['current_price'] = signals_df['close']
signals_df['price_change_pct'] = (signals_df['predicted_price'] - signals_df['current_price']) / signals_df['current_price'] * 100

# Generate trading signals
threshold = 1.5  # 1.5% threshold
signals_df['signal'] = 0
signals_df.loc[signals_df['price_change_pct'] > threshold, 'signal'] = 1  # Buy
signals_df.loc[signals_df['price_change_pct'] < -threshold, 'signal'] = -1  # Sell

signal_map = {-1: 'SELL', 0: 'HOLD', 1: 'BUY'}
signals_df['signal_name'] = signals_df['signal'].map(signal_map)
signals_df['confidence'] = np.abs(signals_df['price_change_pct']) / 5.0
signals_df['confidence'] = np.clip(signals_df['confidence'], 0, 1)

# Display recent signals
recent_signals = signals_df[signals_df['signal'] != 0].tail(10)

print(f"\n🔍 Recent Trading Signals:")
print(f"{'Date':<12} {'Signal':<6} {'Current':<10} {'Predicted':<10} {'Change%':<8} {'Confidence':<10}")
print("-" * 70)

for idx, row in recent_signals.iterrows():
    signal_emoji = "🟢" if row['signal'] == 1 else "🔴"
    print(f"{row['Date'].strftime('%Y-%m-%d'):<12} {signal_emoji} {row['signal_name']:<4} "
          f"${row['current_price']:<9.2f} ${row['predicted_price']:<9.2f} "
          f"{row['price_change_pct']:<7.2f}% {row['confidence']:<9.3f}")

signal_counts = signals_df['signal_name'].value_counts()
print(f"\n📊 Signal Distribution: {dict(signal_counts)}")

# =============================================================================
# SAVE RESULTS
# =============================================================================

timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")

# Save comprehensive results
results = {
    'timestamp': timestamp,
    'platform': 'Kaggle GPU',
    'training_time_seconds': training_time.total_seconds(),
    'data_info': {
        'num_days': len(btc_processed),
        'date_range': f"{btc_processed['Date'].min()} to {btc_processed['Date'].max()}",
        'price_range': f"${btc_processed['close'].min():.2f} to ${btc_processed['close'].max():.2f}",
        'features_count': len(lstm_model.features)
    },
    'model_performance': {
        'rmse': float(metrics['rmse']),
        'mae': float(metrics['mae']),
        'mape': float(metrics['mape']),
        'directional_accuracy': float(metrics['directional_accuracy'])
    },
    'trading_signals': {
        'total_signals': len(signals_df[signals_df['signal'] != 0]),
        'buy_signals': len(signals_df[signals_df['signal'] == 1]),
        'sell_signals': len(signals_df[signals_df['signal'] == -1]),
        'signal_distribution': dict(signal_counts)
    },
    'training_summary': {
        'epochs_trained': len(history.history['loss']),
        'final_loss': float(history.history['loss'][-1]),
        'final_val_loss': float(history.history['val_loss'][-1]),
        'best_val_loss': float(min(history.history['val_loss']))
    }
}

# Save to JSON
with open(f'kaggle_bitcoin_lstm_results_{timestamp}.json', 'w') as f:
    json.dump(results, f, indent=2)

# Save model
lstm_model.model.save(f'kaggle_bitcoin_lstm_model_{timestamp}.h5')

# Save signals
signals_df[['Date', 'current_price', 'predicted_price', 'price_change_pct', 
           'signal', 'signal_name', 'confidence']].to_csv(f'trading_signals_{timestamp}.csv', index=False)

print(f"\n✅ KAGGLE TRAINING COMPLETED SUCCESSFULLY!")
print(f"📁 Files saved:")
print(f"  • Model: kaggle_bitcoin_lstm_model_{timestamp}.h5")
print(f"  • Results: kaggle_bitcoin_lstm_results_{timestamp}.json")
print(f"  • Signals: trading_signals_{timestamp}.csv")

print(f"\n🎯 Final Performance Summary:")
print(f"  • RMSE: ${metrics['rmse']:.2f}")
print(f"  • Directional Accuracy: {metrics['directional_accuracy']:.1f}%")
print(f"  • Training Time: {training_time}")
print(f"  • Total Signals: {len(signals_df[signals_df['signal'] != 0])}")

print(f"\n🚀 Model ready for deployment! Happy trading! 📈")
