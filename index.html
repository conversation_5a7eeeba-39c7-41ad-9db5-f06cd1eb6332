<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Bitcoin Quant Trading System - NewsProcessor</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            color: #333;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        
        .header {
            text-align: center;
            color: white;
            margin-bottom: 40px;
        }
        
        .header h1 {
            font-size: 3rem;
            margin-bottom: 10px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }
        
        .header p {
            font-size: 1.2rem;
            opacity: 0.9;
        }
        
        .card {
            background: white;
            border-radius: 15px;
            padding: 30px;
            margin-bottom: 30px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            transition: transform 0.3s ease;
        }
        
        .card:hover {
            transform: translateY(-5px);
        }
        
        .features {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 30px;
            margin-bottom: 40px;
        }
        
        .feature {
            text-align: center;
        }
        
        .feature-icon {
            font-size: 3rem;
            margin-bottom: 15px;
        }
        
        .feature h3 {
            color: #667eea;
            margin-bottom: 15px;
        }
        
        .cta {
            text-align: center;
            background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
            color: white;
        }
        
        .btn {
            display: inline-block;
            background: #667eea;
            color: white;
            padding: 15px 30px;
            text-decoration: none;
            border-radius: 50px;
            font-weight: bold;
            margin: 10px;
            transition: all 0.3s ease;
        }
        
        .btn:hover {
            background: #5a67d8;
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
        }
        
        .tech-stack {
            display: flex;
            flex-wrap: wrap;
            justify-content: center;
            gap: 15px;
            margin-top: 20px;
        }
        
        .tech-badge {
            background: #f7fafc;
            color: #4a5568;
            padding: 8px 16px;
            border-radius: 20px;
            font-size: 0.9rem;
            border: 2px solid #e2e8f0;
        }
        
        .status {
            background: #48bb78;
            color: white;
            padding: 10px 20px;
            border-radius: 25px;
            display: inline-block;
            font-weight: bold;
            margin-bottom: 20px;
        }
        
        @media (max-width: 768px) {
            .header h1 {
                font-size: 2rem;
            }
            
            .container {
                padding: 10px;
            }
            
            .card {
                padding: 20px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🚀 Bitcoin Quant Trading System</h1>
            <p>Professional Cryptocurrency Trading Dashboard & Analytics Platform</p>
            <div class="status">✅ LIVE & OPERATIONAL</div>
        </div>
        
        <div class="card">
            <h2>🎯 Project Overview</h2>
            <p>A comprehensive Bitcoin quantitative trading system built with modern Python technologies. Features real-time data analysis, interactive dashboards, and extensible architecture for machine learning models.</p>
            
            <div class="tech-stack">
                <span class="tech-badge">Python 3.13</span>
                <span class="tech-badge">Streamlit</span>
                <span class="tech-badge">YFinance</span>
                <span class="tech-badge">Plotly</span>
                <span class="tech-badge">Pandas</span>
                <span class="tech-badge">NumPy</span>
            </div>
        </div>
        
        <div class="features">
            <div class="card feature">
                <div class="feature-icon">📊</div>
                <h3>Real-Time Dashboard</h3>
                <p>Interactive Bitcoin price monitoring with candlestick charts, volume analysis, and live data quality metrics.</p>
            </div>
            
            <div class="card feature">
                <div class="feature-icon">📈</div>
                <h3>Multi-Timeframe Analysis</h3>
                <p>Support for multiple timeframes (7d, 30d, 90d, 1y) and intervals (1d, 1h, 5m) for comprehensive market analysis.</p>
            </div>
            
            <div class="card feature">
                <div class="feature-icon">🔧</div>
                <h3>Professional Architecture</h3>
                <p>Clean, modular codebase with modern Python packaging, virtual environments, and comprehensive testing framework.</p>
            </div>
        </div>
        
        <div class="card cta">
            <h2>🚀 Get Started</h2>
            <p>Ready to explore Bitcoin quantitative trading? Clone the repository and start analyzing cryptocurrency markets!</p>
            
            <a href="https://github.com/newsprocessor/newsprocessor.github.io" class="btn">View on GitHub</a>
            <a href="https://github.com/newsprocessor/newsprocessor.github.io/blob/main/README_BITCOIN_QUANT.md" class="btn">Documentation</a>
            <a href="https://github.com/newsprocessor/newsprocessor.github.io/blob/main/SETUP_COMPLETE.md" class="btn">Setup Guide</a>
        </div>
        
        <div class="card">
            <h2>🎉 Current Status</h2>
            <ul style="list-style: none; padding: 0;">
                <li style="margin: 10px 0;">✅ <strong>Virtual Environment:</strong> Python 3.13.2 ready</li>
                <li style="margin: 10px 0;">✅ <strong>Dependencies:</strong> All packages installed and tested</li>
                <li style="margin: 10px 0;">✅ <strong>Dashboard:</strong> Streamlit app fully operational</li>
                <li style="margin: 10px 0;">✅ <strong>Data Pipeline:</strong> YFinance integration working</li>
                <li style="margin: 10px 0;">✅ <strong>Testing:</strong> Comprehensive test suite passing</li>
                <li style="margin: 10px 0;">🚀 <strong>Ready for:</strong> ML models, backtesting, additional providers</li>
            </ul>
        </div>
        
        <div style="text-align: center; color: white; margin-top: 40px; opacity: 0.8;">
            <p>Built with ❤️ by NewsProcessor | © 2025</p>
        </div>
    </div>
</body>
</html>
