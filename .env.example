# OpenBB API Keys (Optional - for enhanced data access)
# Get your free API keys from the respective providers

# Tiingo API Key (Free tier available)
# Sign up at: https://api.tiingo.com/
TIINGO_API_KEY=your_tiingo_api_key_here

# Alpha Vantage API Key (Free tier available)
# Sign up at: https://www.alphavantage.co/support/#api-key
ALPHA_VANTAGE_API_KEY=your_alpha_vantage_api_key_here

# Financial Modeling Prep API Key (Optional)
# Sign up at: https://financialmodelingprep.com/developer/docs
FMP_API_KEY=your_fmp_api_key_here

# Polygon API Key (Optional)
# Sign up at: https://polygon.io/
POLYGON_API_KEY=your_polygon_api_key_here

# MLflow Tracking (Optional - for experiment tracking)
MLFLOW_TRACKING_URI=http://localhost:5000

# Weights & Biases (Optional - for experiment tracking)
WANDB_API_KEY=your_wandb_api_key_here
WANDB_PROJECT=bitcoin-quant-trading

# Database Configuration (Optional)
DATABASE_URL=sqlite:///data/trading_data.db

# Logging Configuration
LOG_LEVEL=INFO
LOG_FILE=logs/bitcoin_quant.log

# Data Cache Configuration
CACHE_ENABLED=true
CACHE_DIRECTORY=data/openbb_cache
CACHE_MAX_AGE_HOURS=24

# Trading Configuration (for future live trading)
PAPER_TRADING=true
MAX_POSITION_SIZE=0.1
RISK_TOLERANCE=0.02
