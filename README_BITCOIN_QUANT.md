# Bitcoin Quant Trading System

A comprehensive Bitcoin quantitative trading system built on the OpenBB Platform, featuring multi-provider data integration, machine learning models, and real-time visualization dashboards.

## 🚀 Features

- **Multi-Provider Data Integration**: Seamlessly access Bitcoin data from YFinance, Tiingo, Alpha Vantage, and more
- **Data Quality Validation**: Comprehensive data validation and cross-provider verification
- **Real-time Dashboard**: Interactive Streamlit dashboard for monitoring and analysis
- **Machine Learning Ready**: Structured for LSTM, DQN, and other ML model implementations
- **OpenBB Native**: Built on OpenBB Platform for professional-grade financial data access
- **Extensible Architecture**: Modular design for easy customization and extension

## 📋 Requirements

- Python 3.9-3.12
- OpenBB Platform
- Streamlit for dashboards
- Standard ML libraries (TensorFlow, PyTorch, scikit-learn)

## 🛠️ Installation

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd bitcoin-quant-openbb
   ```

2. **Create virtual environment**
   ```bash
   python -m venv venv
   source venv/bin/activate  # On Windows: venv\Scripts\activate
   ```

3. **Install dependencies**
   ```bash
   pip install -r requirements.txt
   ```

4. **Set up environment variables** (optional)
   ```bash
   cp .env.example .env
   # Edit .env with your API keys
   ```

## 🚀 Quick Start

### 1. Test Data Pipeline
```bash
python src/main.py --mode data --symbol BTCUSD --days 30
```

### 2. Validate Multi-Provider Data
```bash
python src/main.py --mode validate --symbol BTCUSD --days 7
```

### 3. Launch Dashboard
```bash
python src/main.py --mode dashboard
# Or directly: streamlit run ui/bitcoin_dashboard.py
```

## 📊 Dashboard Features

The Streamlit dashboard provides:

- **Real-time Bitcoin price charts** with candlestick visualization
- **Data quality metrics** across multiple providers
- **Volume analysis** and trading statistics
- **Provider comparison** and validation scores
- **Interactive controls** for timeframes and data sources

## 🏗️ Project Structure

```
bitcoin-quant-openbb/
├── src/
│   ├── data/                    # Data access and validation
│   │   ├── openbb_client.py     # OpenBB data wrapper
│   │   ├── crypto_providers.py  # Multi-provider management
│   │   └── data_validation.py   # Data quality validation
│   ├── features/                # Feature engineering (planned)
│   ├── models/                  # ML models (planned)
│   ├── backtesting/            # Backtesting engine (planned)
│   └── utils/                  # Utility functions
├── ui/                         # Streamlit dashboards
│   ├── bitcoin_dashboard.py    # Main dashboard
│   ├── pages/                  # Dashboard pages
│   └── components/             # Reusable components
├── notebooks/                  # Jupyter notebooks
├── data/                       # Data storage
├── config/                     # Configuration files
├── requirements.txt            # Python dependencies
└── pyproject.toml             # Modern Python packaging
```

## 🔧 Configuration

### Provider Configuration
Edit `config/openbb_providers.yaml` to configure data providers:

```yaml
providers:
  yfinance:
    enabled: true
    priority: 1
    symbols: ["BTC-USD", "ETH-USD"]
  tiingo:
    enabled: true
    priority: 2
    api_key_required: true
```

### API Keys
Set up API keys in `.env` file for enhanced data access:

```bash
TIINGO_API_KEY=your_key_here
ALPHA_VANTAGE_API_KEY=your_key_here
```

## 📈 Usage Examples

### Basic Data Access
```python
from src.data.crypto_providers import CryptoProviderManager

# Initialize provider manager
provider_manager = CryptoProviderManager()

# Get Bitcoin data
data, provider = provider_manager.get_best_data(
    symbol="BTCUSD",
    days=30
)

print(f"Got {len(data)} records from {provider}")
```

### Multi-Provider Validation
```python
from src.data.data_validation import DataValidator

validator = DataValidator()

# Get data from multiple providers
provider_data = provider_manager.get_cross_provider_validation("BTCUSD")

# Validate across providers
scores = validator.cross_validate_providers(provider_data)
print("Validation scores:", scores)
```

## 🧪 Development

### Running Tests
```bash
pytest tests/
```

### Code Quality
```bash
black src/
isort src/
flake8 src/
mypy src/
```

### Adding New Features
1. Follow the modular architecture
2. Add tests for new functionality
3. Update documentation
4. Ensure OpenBB integration compatibility

## 📚 Documentation

- [OpenBB Platform Documentation](https://docs.openbb.co/platform)
- [Project Plan](PROJECT_PLAN.md) - Detailed implementation roadmap
- [API Reference](docs/api.md) - Coming soon

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests
5. Submit a pull request

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🙏 Acknowledgments

- Built on the [OpenBB Platform](https://openbb.co/)
- Inspired by quantitative trading best practices
- Community-driven development

---

**⚠️ Disclaimer**: This is a research and educational project. Not financial advice. Use at your own risk.
