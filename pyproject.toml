[build-system]
requires = ["setuptools>=61.0", "wheel"]
build-backend = "setuptools.build_meta"

[project]
name = "bitcoin-quant-openbb"
version = "0.1.0"
description = "Bitcoin Quantitative Trading System using OpenBB Platform"
authors = [
    {name = "Your Name", email = "<EMAIL>"}
]
readme = "README.md"
license = {text = "MIT"}
requires-python = ">=3.9,<3.13"
keywords = ["bitcoin", "cryptocurrency", "quantitative", "trading", "openbb", "machine-learning"]
classifiers = [
    "Development Status :: 3 - Alpha",
    "Intended Audience :: Financial and Insurance Industry",
    "License :: OSI Approved :: MIT License",
    "Programming Language :: Python :: 3",
    "Programming Language :: Python :: 3.9",
    "Programming Language :: Python :: 3.10",
    "Programming Language :: Python :: 3.11",
    "Programming Language :: Python :: 3.12",
    "Topic :: Office/Business :: Financial :: Investment",
    "Topic :: Scientific/Engineering :: Artificial Intelligence",
]

dependencies = [
    "openbb[all]>=4.0.0",
    "openbb-charting>=1.0.0",
    "pandas>=2.0.0",
    "numpy>=1.24.0",
    "scikit-learn>=1.3.0",
    "tensorflow>=2.13.0",
    "torch>=2.0.0",
    "streamlit>=1.28.0",
    "plotly>=5.15.0",
    "stable-baselines3>=2.0.0",
    "mlflow>=2.7.0",
    "python-dotenv>=1.0.0",
    "pydantic>=2.0.0",
    "pyyaml>=6.0",
    "ta-lib>=0.4.25",
    "pandas-ta>=0.3.14b",
    "vectorbt>=0.25.0",
    "great-expectations>=0.17.0",
    "loguru>=0.7.0",
]

[project.optional-dependencies]
dev = [
    "pytest>=7.4.0",
    "pytest-cov>=4.1.0",
    "black>=23.7.0",
    "isort>=5.12.0",
    "flake8>=6.0.0",
    "mypy>=1.5.0",
    "jupyter>=1.0.0",
    "ipykernel>=6.25.0",
]

visualization = [
    "matplotlib>=3.7.0",
    "seaborn>=0.12.0",
    "wandb>=0.15.0",
]

backtesting = [
    "zipline-reloaded>=3.0.0",
    "vectorbt>=0.25.0",
]

[project.urls]
Homepage = "https://github.com/yourusername/bitcoin-quant-openbb"
Repository = "https://github.com/yourusername/bitcoin-quant-openbb"
Documentation = "https://github.com/yourusername/bitcoin-quant-openbb#readme"
"Bug Tracker" = "https://github.com/yourusername/bitcoin-quant-openbb/issues"

[project.scripts]
btc-quant = "src.main:main"

[tool.setuptools.packages.find]
where = ["."]
include = ["src*"]

[tool.black]
line-length = 88
target-version = ['py39', 'py310', 'py311', 'py312']
include = '\.pyi?$'
extend-exclude = '''
/(
  # directories
  \.eggs
  | \.git
  | \.hg
  | \.mypy_cache
  | \.tox
  | \.venv
  | build
  | dist
)/
'''

[tool.isort]
profile = "black"
multi_line_output = 3
line_length = 88
known_first_party = ["src"]

[tool.mypy]
python_version = "3.9"
warn_return_any = true
warn_unused_configs = true
disallow_untyped_defs = true
ignore_missing_imports = true

[tool.pytest.ini_options]
testpaths = ["tests"]
python_files = ["test_*.py"]
python_classes = ["Test*"]
python_functions = ["test_*"]
addopts = [
    "--cov=src",
    "--cov-report=html",
    "--cov-report=term-missing",
    "--strict-markers",
]

[tool.coverage.run]
source = ["src"]
omit = [
    "*/tests/*",
    "*/test_*",
    "setup.py",
]

[tool.coverage.report]
exclude_lines = [
    "pragma: no cover",
    "def __repr__",
    "raise AssertionError",
    "raise NotImplementedError",
]
